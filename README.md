## 🎮 AirSim Joystick/Gamepad Kontrol Sistemi

Bu proje AirSim simülasyonunda joystick/gamepad ile drone kontrolü sağlar.

### Sanal Ortam Komutları:
```powershell
# Sanal ortam başlatma
venv\Scripts\activate

# Sanal ortam kapatma
deactivate
```

### Gere<PERSON><PERSON>üphaneleri Yükleme:
```powershell
pip install -r requirements-dev.txt
```

### Kullanım:
```powershell
# Joystick testi
python scripts/test_joystick.py

# Joystick ile drone kontrolü
python scripts/joystick_controller.py

# Klavye ile demo (joystick yoksa)
python scripts/keyboard_demo.py
```

### Detaylı Rehber:
`docs/joystick_control_guide.md` dosyasını inceleyin.

DroneProject/
├── AirSim/                # AirSim'un resmi kaynak kodları (git clone ile alınır)
├── ardupilot/             # ArduPilot'un resmi kaynak kodları (git clone ile alınır)
├── docs/                  # Proje belge<PERSON>, notlar
├── logs/                  # Simülasyon ya da uçuş gün<PERSON>kleri (tlog, bin vs.)
├── missions/              # Görev planları (waypoint dosyaları, .waypoints)
├── models/                # Simülasyon modelleri (örneğin Gazebo, SITL özel araçlar)
├── scripts/               # Python betikleri, kontrol kodları
├── requirements.txt       # Proje ana bağımlılıkları
├── requirements-dev.txt   # Geliştirici bağımlılıkları
├── setup_env.ps1          # Ortam kurulum scripti (PowerShell)
└── README.md              # Proje hakkında genel bilgi


Klasör / Dosya	Ne için kullanılır?

AirSim/     AirSim'un resmi kaynak kodları (git clone ile alınır)
ardupilot/  ArduPilot’un resmi kaynak kodları (git ile klonlanır)
docs/	Kurulum notları, kullanım belgeleri
logs/	Telemetri günlükleri (uçuş verisi analizi için)
missions/	Mission Planner veya QGroundControl'den alınan görev dosyaları (.waypoint)
models/	Simülasyon ortamları, özel dronelara ait .xml veya .xacro dosyaları
scripts/	Drone'u kontrol eden kendi yazdığın Python dosyaları
requirements.txt	Proje ana bağımlılıkları
requirements-dev.txt	Geliştirici bağımlılıkları
setup_env.ps1	Ortam kurulum scripti (PowerShell)
README.md	Projenin açıklaması, çalışma talimatları
venv/	Proje bağımlılıklarını izole eden Python sanal ortam klasörü

