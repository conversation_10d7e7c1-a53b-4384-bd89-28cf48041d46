"""
Joystick/Gamepad konfigürasyon ayarları
Bu dosya joystick buton ve axis eşlemelerini içerir
"""

class JoystickConfig:
    """Joystick konfigürasyon sınıfı"""
    
    def __init__(self):
        # Axis eşlemeleri (genellikle Xbox/PS4 controller için)
        self.AXIS_PITCH = 1      # Sol analog stick Y ekseni (yukarı/aşağı)
        self.AXIS_ROLL = 0       # Sol analog stick X ekseni (sol/sağ)
        self.AXIS_THROTTLE = 4   # Sol trigger (LT) - throttle up
        self.AXIS_YAW = 3        # Sağ analog stick X ekseni (yaw)
        
        # Alternatif throttle kontrolü için sağ analog stick Y ekseni
        self.AXIS_THROTTLE_ALT = 2  # Sağ analog stick Y ekseni
        
        # Buton eşlemeleri (Xbox controller için)
        self.BUTTON_ARM_DISARM = 0    # A butonu
        self.BUTTON_TAKEOFF = 1       # B butonu  
        self.BUTTON_LAND = 2          # X butonu
        self.BUTTON_EMERGENCY = 3     # Y butonu
        self.BUTTON_MODE_SWITCH = 4   # LB (Sol bumper)
        self.BUTTON_RESET = 5         # RB (Sağ bumper)
        
        # Kontrol hassasiyeti ayarları
        self.PITCH_SENSITIVITY = 1.0
        self.ROLL_SENSITIVITY = 1.0
        self.YAW_SENSITIVITY = 1.0
        self.THROTTLE_SENSITIVITY = 1.0
        
        # Dead zone ayarları (küçük joystick hareketlerini yok sayar)
        self.DEAD_ZONE = 0.1
        
        # Maksimum değerler
        self.MAX_PITCH_ROLL = 1.0    # -1.0 ile 1.0 arası
        self.MAX_YAW_RATE = 1.0      # -1.0 ile 1.0 arası
        self.MAX_THROTTLE = 1.0      # 0.0 ile 1.0 arası
        
        # Güvenlik ayarları
        self.ENABLE_EMERGENCY_STOP = True
        self.AUTO_DISARM_ON_DISCONNECT = True
        
    def apply_dead_zone(self, value):
        """Dead zone uygula"""
        if abs(value) < self.DEAD_ZONE:
            return 0.0
        return value
    
    def normalize_axis(self, value, sensitivity=1.0):
        """Axis değerini normalize et"""
        value = self.apply_dead_zone(value)
        return max(-1.0, min(1.0, value * sensitivity))
    
    def get_button_name(self, button_id):
        """Buton ID'sine göre buton adını döndür"""
        button_names = {
            self.BUTTON_ARM_DISARM: "ARM/DISARM",
            self.BUTTON_TAKEOFF: "TAKEOFF", 
            self.BUTTON_LAND: "LAND",
            self.BUTTON_EMERGENCY: "EMERGENCY",
            self.BUTTON_MODE_SWITCH: "MODE_SWITCH",
            self.BUTTON_RESET: "RESET"
        }
        return button_names.get(button_id, f"BUTTON_{button_id}")
    
    def get_axis_name(self, axis_id):
        """Axis ID'sine göre axis adını döndür"""
        axis_names = {
            self.AXIS_PITCH: "PITCH",
            self.AXIS_ROLL: "ROLL",
            self.AXIS_THROTTLE: "THROTTLE",
            self.AXIS_YAW: "YAW",
            self.AXIS_THROTTLE_ALT: "THROTTLE_ALT"
        }
        return axis_names.get(axis_id, f"AXIS_{axis_id}")

# Xbox Controller buton eşlemeleri referansı:
"""
Xbox Controller Layout:
Buttons:
0 - A
1 - B  
2 - X
3 - Y
4 - LB (Left Bumper)
5 - RB (Right Bumper)
6 - Back/View
7 - Start/Menu
8 - Left Stick Click
9 - Right Stick Click

Axes:
0 - Left Stick X (Sol/Sağ)
1 - Left Stick Y (Yukarı/Aşağı) 
2 - Right Stick Y (Yukarı/Aşağı)
3 - Right Stick X (Sol/Sağ)
4 - Left Trigger (LT)
5 - Right Trigger (RT)
"""
