argv = ['/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waf-light', 'configure', '--board', 'sitl']
config_cmd = 'configure'
environ = {'ProgramFiles(x86)': 'C:\\Program Files (x86)', '!::': '::\\', 'CommonProgramFiles(x86)': 'C:\\Program Files (x86)\\Common Files', 'SHELL': '/bin/bash', 'NUMBER_OF_PROCESSORS': '20', 'PROCESSOR_LEVEL': '6', 'TERM_PROGRAM_VERSION': '3.7.8', 'EFC_7384_2283032206': '1', 'USERDOMAIN_ROAMINGPROFILE': 'POLKIP', 'HOSTNAME': 'Polkip', 'PROGRAMFILES': 'C:\\Program Files', 'sqlite': 'C:\\sqlite', 'ChocolateyInstall': 'C:\\ProgramData\\chocolatey', 'PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW', 'OS': 'Windows_NT', 'HOMEDRIVE': 'C:', 'USERDOMAIN': 'POLKIP', 'PWD': '/home/<USER>/teknofest/ardupilot/ArduCopter', 'USERPROFILE': 'C:\\Users\\<USER>\\Users\\pc\\OneDrive', 'PRINTER': 'Canon MF440 Series UFR II', 'TZ': 'Europe/Istanbul', 'ALLUSERSPROFILE': 'C:\\ProgramData', 'ORIGINAL_PATH': '/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/Python313/Scripts:/cygdrive/c/Python313:/cygdrive/c/WINDOWS/system32:/cygdrive/c/WINDOWS:/cygdrive/c/WINDOWS/System32/Wbem:/cygdrive/c/WINDOWS/System32/WindowsPowerShell/v1.0:/cygdrive/c/WINDOWS/System32/OpenSSH:/cygdrive/c/MinGW/bin:/cygdrive/c/Program Files/Git/cmd:/cygdrive/c/Program Files/Microsoft SQL Server/150/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn:/cygdrive/c/sqlite:/cygdrive/c/Program Files (x86)/Microsoft SQL Server/160/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/160/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/160/DTS/Binn:/cygdrive/c/Program Files (x86)/Microsoft SQL Server/160/DTS/Binn:/cygdrive/c/xampp/php:/cygdrive/c/ProgramData/ComposerSetup/bin:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Program Files/nodejs:/cygdrive/c/ProgramData/chocolatey/bin:/cygdrive/c/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR:/cygdrive/c/Program Files (x86)/NVIDIA Corporation/PhysX/Common:/cygdrive/c/Program Files/PostgreSQL/17/bin:/cygdrive/c/Program Files/Java/jdk-21/bin:/cygdrive/c/ProgramData/chocolatey/lib/maven/apache-maven-3.9.9/bin:/cygdrive/c/Program Files (x86)/MAVProxy:/cygdrive/c/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2020-q4-major/bin:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python310/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python310:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python311:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin:/cygdrive/c/Users/<USER>/AppData/Roaming/Composer/vendor/bin:/cygdrive/c/Users/<USER>/.dotnet/tools:/cygdrive/c/Users/<USER>/AppData/Local/GitHubDesktop/bin:/cygdrive/c/Users/<USER>/AppData/Roaming/npm:/cygdrive/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/cygdrive/c/Program Files/PostgreSQL/17/bin:/cygdrive/c/Program Files/Java/jdk-21/bin:/cygdrive/c/ProgramData/chocolatey/lib/maven/apache-maven-3.9.9/bin', 'CommonProgramW6432': 'C:\\Program Files\\Common Files', 'HOME': '/home/<USER>', 'USERNAME': 'Enes', 'OneDrive': 'C:\\Users\\<USER>\\OneDrive', 'COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe', 'APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming', 'SYSTEMROOT': 'C:\\WINDOWS', 'LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local', 'COMPUTERNAME': 'POLKIP', 'INFOPATH': '/usr/local/info:/usr/share/info:/usr/info', 'EFC_7384_1592913036': '1', 'IGCCSVC_DB': 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAj/44BHCuS0Gi0AUm49H1RwQAAAACAAAAAAAQZgAAAAEAACAAAADZcTa6vnp4LTPzhfaIytoMi8D8eS6BaZ392bX0Rb/62QAAAAAOgAAAAAIAACAAAADb7Av4OCjvtAa8Xntd/v1QGm1PAHXGpLWpGv3OV0YID2AAAADTcAau2istaSjMXveuP/g3FQbEb+NhBsdbJJF9JiBKUiMPmKV0O1PLL/fkdjWDIfWr1BVKqaLTqOMMisBwlZd6+ctfNStOBmUpZuIVmfmwtTrf+2vd/5eHHpFTd5o0ndZAAAAAFvIawDHjyhFW6VITahhI9ZXAtMxWMxD74DJ3u8ixgWH/rLzzcI9lzLZWGZddyXmWLWICFHf6b84oQIB3jgKx+g==', 'TERM': 'vt100', 'LOGONSERVER': '\\\\POLKIP', 'ZES_ENABLE_SYSMAN': '1', 'USER': 'Enes', 'PSModulePath': 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules;C:\\Program Files (x86)\\Microsoft SQL Server\\160\\Tools\\PowerShell\\Modules\\', 'TEMP': '/tmp', 'SHLVL': '1', 'PROCESSOR_REVISION': '9a03', 'DriverData': 'C:\\Windows\\System32\\Drivers\\DriverData', 'PT8HOME': 'C:\\Program Files\\Cisco Packet Tracer 8.2.2', 'COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files', 'LC_CTYPE': 'tr_TR.UTF-8', 'PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 154 Stepping 3, GenuineIntel', 'SESSIONNAME': 'Console', 'PS1': '\\[\\e]0;\\w\\a\\]\\n\\[\\e[32m\\]\\u@\\h \\[\\e[33m\\]\\w\\[\\e[0m\\]\\n\\$ ', 'HOMEPATH': '\\Users\\pc', 'TMP': '/tmp', 'PATH': '/usr/local/bin:/usr/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/Python313/Scripts:/cygdrive/c/Python313:/cygdrive/c/WINDOWS/system32:/cygdrive/c/WINDOWS:/cygdrive/c/WINDOWS/System32/Wbem:/cygdrive/c/WINDOWS/System32/WindowsPowerShell/v1.0:/cygdrive/c/WINDOWS/System32/OpenSSH:/cygdrive/c/MinGW/bin:/cygdrive/c/Program Files/Git/cmd:/cygdrive/c/Program Files/Microsoft SQL Server/150/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn:/cygdrive/c/sqlite:/cygdrive/c/Program Files (x86)/Microsoft SQL Server/160/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/160/Tools/Binn:/cygdrive/c/Program Files/Microsoft SQL Server/160/DTS/Binn:/cygdrive/c/Program Files (x86)/Microsoft SQL Server/160/DTS/Binn:/cygdrive/c/xampp/php:/cygdrive/c/ProgramData/ComposerSetup/bin:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Program Files/nodejs:/cygdrive/c/ProgramData/chocolatey/bin:/cygdrive/c/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR:/cygdrive/c/Program Files (x86)/NVIDIA Corporation/PhysX/Common:/cygdrive/c/Program Files/PostgreSQL/17/bin:/cygdrive/c/Program Files/Java/jdk-21/bin:/cygdrive/c/ProgramData/chocolatey/lib/maven/apache-maven-3.9.9/bin:/cygdrive/c/Program Files (x86)/MAVProxy:/cygdrive/c/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2020-q4-major/bin:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python310/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python310:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python311:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin:/cygdrive/c/Users/<USER>/AppData/Roaming/Composer/vendor/bin:/cygdrive/c/Users/<USER>/.dotnet/tools:/cygdrive/c/Users/<USER>/AppData/Local/GitHubDesktop/bin:/cygdrive/c/Users/<USER>/AppData/Roaming/npm:/cygdrive/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/cygdrive/c/Program Files/PostgreSQL/17/bin:/cygdrive/c/Program Files/Java/jdk-21/bin:/cygdrive/c/ProgramData/chocolatey/lib/maven/apache-maven-3.9.9/bin', 'ProgramW6432': 'C:\\Program Files', 'PROFILEREAD': 'true', 'MINTTY_SHORTCUT': '/cygdrive/c/Users/<USER>/Desktop/Cygwin64 Terminal.lnk', 'WINDIR': 'C:\\WINDOWS', 'PROCESSOR_ARCHITECTURE': 'AMD64', 'PUBLIC': 'C:\\Users\\<USER>\\ProgramData', 'ChocolateyLastPathUpdate': '133930246219783807', '_': '../Tools/autotest/sim_vehicle.py', 'SIM_VEHICLE_SESSION': '5ad7964311622343'}
files = ['/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_Scripting/wscript', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_GPS/wscript', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_HAL_SITL/wscript', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/SITL/wscript', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_Networking/wscript', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_DDS/wscript', '/usr/lib/python3.9/importlib/_bootstrap.py', '/usr/lib/python3.9/importlib/_bootstrap_external.py', '/usr/lib/python3.9/codecs.py', '/usr/lib/python3.9/encodings/aliases.py', '/usr/lib/python3.9/encodings/__init__.py', '/usr/lib/python3.9/encodings/utf_8.py', '/usr/lib/python3.9/encodings/latin_1.py', '/usr/lib/python3.9/abc.py', '/usr/lib/python3.9/io.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waf-light', '/usr/lib/python3.9/stat.py', '/usr/lib/python3.9/_collections_abc.py', '/usr/lib/python3.9/genericpath.py', '/usr/lib/python3.9/posixpath.py', '/usr/lib/python3.9/os.py', '/usr/lib/python3.9/_sitebuiltins.py', '/usr/lib/python3.9/_bootlocale.py', '/usr/lib/python3.9/site-packages/_distutils_hack/__init__.py', '/usr/lib/python3.9/types.py', '/usr/lib/python3.9/warnings.py', '/usr/lib/python3.9/importlib/__init__.py', '/usr/lib/python3.9/importlib/machinery.py', '/usr/lib/python3.9/lib-dynload/_heapq.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/heapq.py', '/usr/lib/python3.9/keyword.py', '/usr/lib/python3.9/operator.py', '/usr/lib/python3.9/reprlib.py', '/usr/lib/python3.9/collections/__init__.py', '/usr/lib/python3.9/collections/abc.py', '/usr/lib/python3.9/functools.py', '/usr/lib/python3.9/contextlib.py', '/usr/lib/python3.9/enum.py', '/usr/lib/python3.9/sre_constants.py', '/usr/lib/python3.9/sre_parse.py', '/usr/lib/python3.9/sre_compile.py', '/usr/lib/python3.9/copyreg.py', '/usr/lib/python3.9/re.py', '/usr/lib/python3.9/typing.py', '/usr/lib/python3.9/importlib/abc.py', '/usr/lib/python3.9/importlib/util.py', '/usr/lib/python3.9/site.py', '/usr/lib/python3.9/ast.py', '/usr/lib/python3.9/lib-dynload/_opcode.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/opcode.py', '/usr/lib/python3.9/dis.py', '/usr/lib/python3.9/token.py', '/usr/lib/python3.9/tokenize.py', '/usr/lib/python3.9/linecache.py', '/usr/lib/python3.9/inspect.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/__init__.py', '/usr/lib/python3.9/__future__.py', '/usr/lib/python3.9/shlex.py', '/usr/lib/python3.9/fnmatch.py', '/usr/lib/python3.9/lib-dynload/zlib.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/_compression.py', '/usr/lib/python3.9/_weakrefset.py', '/usr/lib/python3.9/threading.py', '/usr/lib/python3.9/lib-dynload/_bz2.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/bz2.py', '/usr/lib/python3.9/lib-dynload/_lzma.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lzma.py', '/usr/lib/python3.9/lib-dynload/grp.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/shutil.py', '/usr/lib/python3.9/traceback.py', '/usr/lib/python3.9/lib-dynload/math.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/_datetime.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/datetime.py', '/usr/lib/python3.9/signal.py', '/usr/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/select.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/selectors.py', '/usr/lib/python3.9/subprocess.py', '/usr/lib/python3.9/platform.py', '/usr/lib/python3.9/lib-dynload/_struct.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/struct.py', '/usr/lib/python3.9/lib-dynload/binascii.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/base64.py', '/usr/lib/python3.9/_compat_pickle.py', '/usr/lib/python3.9/lib-dynload/_pickle.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/pickle.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Errors.py', '/usr/lib/python3.9/lib-dynload/_hashlib.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/_blake2.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/hashlib.py', '/usr/lib/python3.9/encodings/hex_codec.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Utils.py', '/usr/lib/python3.9/weakref.py', '/usr/lib/python3.9/copy.py', '/usr/lib/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/ctypes/_endian.py', '/usr/lib/python3.9/ctypes/__init__.py', '/usr/lib/python3.9/lib-dynload/fcntl.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/termios.cpython-39-x86_64-cygwin.dll', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/ansiterm.py', '/usr/lib/python3.9/string.py', '/usr/lib/python3.9/logging/__init__.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Logs.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/ConfigSet.py', '/usr/lib/python3.9/lib-dynload/_bisect.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/bisect.py', '/usr/lib/python3.9/lib-dynload/_random.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/_sha512.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/random.py', '/usr/lib/python3.9/tempfile.py', '/usr/lib/python3.9/textwrap.py', '/usr/lib/python3.9/gettext.py', '/usr/lib/python3.9/locale.py', '/usr/lib/python3.9/optparse.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Node.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Context.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Options.py', '/usr/lib/python3.9/lib-dynload/_queue.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/queue.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Task.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Runner.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/TaskGen.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Build.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Configure.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Scripting.py', '/usr/lib/python3.9/lib-dynload/_json.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/json/scanner.py', '/usr/lib/python3.9/json/decoder.py', '/usr/lib/python3.9/json/encoder.py', '/usr/lib/python3.9/json/__init__.py', '/usr/lib/python3.9/ntpath.py', '/usr/lib/python3.9/urllib/__init__.py', '/usr/lib/python3.9/urllib/parse.py', '/usr/lib/python3.9/pathlib.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/ap_persistent.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/ardupilotwaf.py', '/usr/lib/python3.9/glob.py', '/usr/lib/python3.9/argparse.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/../../libraries/AP_HAL_ChibiOS/hwdef/scripts/dma_resolver.py', '/usr/lib/python3.9/filecmp.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/libraries/AP_HAL_ChibiOS/hwdef/scripts/../../../../libraries/AP_HAL/hwdef/scripts/hwdef.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/../../libraries/AP_HAL_ChibiOS/hwdef/scripts/chibios_hwdef.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/scripts/build_options.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/boards.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/__init__.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c_aliases.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c_preproc.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c_config.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c_osx.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c_tests.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/ccroot.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/compiler_cxx.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/ar.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/gxx.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/clangxx.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/compiler_c.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/extras/__init__.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/xlc.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/extras/c_bgxlc.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/gcc.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/extras/c_emscripten.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/extras/c_nec.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/clang.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/waf_unit_test.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/python.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/build_summary.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/ap_library.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/toolchain.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/cxx.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/c.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/Tools/asm.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/modules/waf/waflib/extras/gccdeps.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/cxx_checks.py', '/usr/lib/python3.9/xml/__init__.py', '/usr/lib/python3.9/xml/etree/__init__.py', '/usr/lib/python3.9/xml/etree/ElementPath.py', '/usr/lib/python3.9/lib-dynload/pyexpat.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/lib-dynload/_elementtree.cpython-39-x86_64-cygwin.dll', '/usr/lib/python3.9/xml/etree/ElementTree.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/mavgen.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/dronecangen.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/git_submodule.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/gtest.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/littlefs.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/Tools/ardupilotwaf/static_linking.py', '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/wscript']
hash = b'\x15\x14\xbf\xcc!\r\xc1\xcb\x1c=\xf7OOh\x9f\x08'
launch_dir = '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot'
options = {'colors': 'auto', 'jobs': 20, 'keep': 0, 'verbose': 0, 'zones': '', 'profile': 0, 'pdb': 0, 'whelp': 0, 'out': '', 'top': '', 'no_lock_in_run': '', 'no_lock_in_out': '', 'no_lock_in_top': '', 'prefix': '/usr', 'bindir': None, 'libdir': None, 'progress_bar': 0, 'targets': '', 'files': '', 'destdir': '', 'force': False, 'distcheck_args': None, 'check_cxx_compiler': None, 'check_c_compiler': None, 'no_tests': False, 'all_tests': False, 'clear_failed_tests': False, 'testcmd': False, 'dump_test_scripts': False, 'pyc': 1, 'pyo': 1, 'nopycache': None, 'python': None, 'pythondir': None, 'pythonarchdir': None, 'program_group': [], 'upload': None, 'upload_port': None, 'upload_blueos': None, 'upload_force': None, 'define': None, 'check_verbose': None, 'clean_all_sigs': None, 'asan': None, 'ubsan': None, 'ubsan_abort': None, 'summary_all': None, 'board': 'sitl', 'debug': False, 'debug_symbols': False, 'vs_launch': False, 'disable_watchdog': False, 'coverage': False, 'Werror': None, 'disable_Werror': None, 'toolchain': None, 'disable_gccdeps': False, 'enable_asserts': False, 'save_temps': False, 'enable_malloc_guard': False, 'enable_stats': False, 'bootloader': False, 'signed_fw': False, 'private_key': None, 'autoconfig': True, 'submodule_update': True, 'enable_header_checks': False, 'default_parameters': None, 'enable_math_check_indexes': False, 'disable_scripting': False, 'enable_scripting': False, 'no_gcs': False, 'scripting_checks': True, 'enable_onvif': False, 'scripting_docs': False, 'enable_opendroneid': False, 'enable_check_firmware': False, 'enable_custom_controller': False, 'enable_gps_logging': False, 'disable_networking': None, 'enable_networking_tests': None, 'enable_dronecan_tests': False, 'sitl_littlefs': False, 'apstatedir': '', 'rsync_dest': '', 'enable_benchmarks': False, 'enable_lttng': False, 'disable_libiio': False, 'disable_tests': False, 'enable_sfml': False, 'enable_sfml_joystick': False, 'enable_sfml_audio': False, 'osd': False, 'osd_fonts': False, 'sitl_osd': False, 'sitl_rgbled': False, 'force_32bit': False, 'build_dates': False, 'sitl_flash_storage': False, 'ekf_double': False, 'ekf_single': False, 'static': False, 'postype_single': False, 'consistent_builds': False, 'extra_hwdef': None, 'assert_cc_version': None, 'num_aux_imus': 0, 'board_start_time': 0, 'enable_iomcu_profiled_support': False, 'enable_new_checking': False, 'enable_AHRS_EXT': False, 'disable_AHRS_EXT': False, 'enable_AHRS_EXT_VECTORNAV': False, 'disable_AHRS_EXT_VECTORNAV': False, 'enable_BARO_WIND_COMP': False, 'disable_BARO_WIND_COMP': False, 'enable_EKF2': False, 'disable_EKF2': False, 'enable_EKF3': False, 'disable_EKF3': False, 'enable_EKF3_EXTNAV': False, 'disable_EKF3_EXTNAV': False, 'enable_EKF3_OPTFLOW': False, 'disable_EKF3_OPTFLOW': False, 'enable_EKF3_WINDEST': False, 'disable_EKF3_WINDEST': False, 'enable_InertialLabs': False, 'disable_InertialLabs': False, 'enable_MicroStrain5': False, 'disable_MicroStrain5': False, 'enable_MicroStrain7': False, 'disable_MicroStrain7': False, 'enable_VISUALODOM': False, 'disable_VISUALODOM': False, 'enable_LONG_TEXT': False, 'disable_LONG_TEXT': False, 'enable_FETTecOneWire': False, 'disable_FETTecOneWire': False, 'enable_HimarkServo': False, 'disable_HimarkServo': False, 'enable_HobbywingESC': False, 'disable_HobbywingESC': False, 'enable_KDECAN': False, 'disable_KDECAN': False, 'enable_RobotisServo': False, 'disable_RobotisServo': False, 'enable_SBUS_Output': False, 'disable_SBUS_Output': False, 'enable_ServoTelem': False, 'disable_ServoTelem': False, 'enable_Volz': False, 'disable_Volz': False, 'enable_Volz_DroneCAN': False, 'disable_Volz_DroneCAN': False, 'enable_ASP5033': False, 'disable_ASP5033': False, 'enable_AUAV_AIRSPEED': False, 'disable_AUAV_AIRSPEED': False, 'enable_Analog': False, 'disable_Analog': False, 'enable_DLVR': False, 'disable_DLVR': False, 'enable_DRONECAN_ASPD': False, 'disable_DRONECAN_ASPD': False, 'enable_MS4525': False, 'disable_MS4525': False, 'enable_MS5525': False, 'disable_MS5525': False, 'enable_MSP_AIRSPEED': False, 'disable_MSP_AIRSPEED': False, 'enable_NMEA_AIRSPEED': False, 'disable_NMEA_AIRSPEED': False, 'enable_SDP3X': False, 'disable_SDP3X': False, 'enable_BARO_PROBEXT': False, 'disable_BARO_PROBEXT': False, 'enable_BARO_TEMPCAL': False, 'disable_BARO_TEMPCAL': False, 'enable_BARO_THRUST_COMP': False, 'disable_BARO_THRUST_COMP': False, 'enable_BMP085': False, 'disable_BMP085': False, 'enable_BMP280': False, 'disable_BMP280': False, 'enable_BMP388': False, 'disable_BMP388': False, 'enable_BMP581': False, 'disable_BMP581': False, 'enable_DPS280': False, 'disable_DPS280': False, 'enable_DRONECAN_BARO': False, 'disable_DRONECAN_BARO': False, 'enable_EXTERNALAHRS_BARO': False, 'disable_EXTERNALAHRS_BARO': False, 'enable_FBM320': False, 'disable_FBM320': False, 'enable_KELLERLD': False, 'disable_KELLERLD': False, 'enable_LPS2XH': False, 'disable_LPS2XH': False, 'enable_MS5607': False, 'disable_MS5607': False, 'enable_MS5611': False, 'disable_MS5611': False, 'enable_MS5637': False, 'disable_MS5637': False, 'enable_MS5837': False, 'disable_MS5837': False, 'enable_MSP_BARO': False, 'disable_MSP_BARO': False, 'enable_SPL06': False, 'disable_SPL06': False, 'enable_BATTERY_ESC_TELEM_OUT': False, 'disable_BATTERY_ESC_TELEM_OUT': False, 'enable_BATTERY_FUELFLOW': False, 'disable_BATTERY_FUELFLOW': False, 'enable_BATTERY_FUELLEVEL_ANALOG': False, 'disable_BATTERY_FUELLEVEL_ANALOG': False, 'enable_BATTERY_FUELLEVEL_PWM': False, 'disable_BATTERY_FUELLEVEL_PWM': False, 'enable_BATTERY_INA2XX': False, 'disable_BATTERY_INA2XX': False, 'enable_BATTERY_INA3221': False, 'disable_BATTERY_INA3221': False, 'enable_BATTERY_SMBUS': False, 'disable_BATTERY_SMBUS': False, 'enable_BATTERY_SUM': False, 'disable_BATTERY_SUM': False, 'enable_BATTERY_SYNTHETIC_CURRENT': False, 'disable_BATTERY_SYNTHETIC_CURRENT': False, 'enable_BATTERY_WATT_MAX': False, 'disable_BATTERY_WATT_MAX': False, 'enable_CAN_Logging': False, 'disable_CAN_Logging': False, 'enable_DroneCAN': False, 'disable_DroneCAN': False, 'enable_Camera': False, 'disable_Camera': False, 'enable_Camera_FOV_Status': False, 'disable_Camera_FOV_Status': False, 'enable_Camera_Info_From_Script': False, 'disable_Camera_Info_From_Script': False, 'enable_Camera_MAVLink': False, 'disable_Camera_MAVLink': False, 'enable_Camera_MAVLinkCamV2': False, 'disable_Camera_MAVLinkCamV2': False, 'enable_Camera_Mount': False, 'disable_Camera_Mount': False, 'enable_Camera_Relay': False, 'disable_Camera_Relay': False, 'enable_Camera_Servo': False, 'disable_Camera_Servo': False, 'enable_Camera_Solo': False, 'disable_Camera_Solo': False, 'enable_Camera_ThermalRange': False, 'disable_Camera_ThermalRange': False, 'enable_RUNCAM': False, 'disable_RUNCAM': False, 'enable_AK09916': False, 'disable_AK09916': False, 'enable_AK8963': False, 'disable_AK8963': False, 'enable_BMM150': False, 'disable_BMM150': False, 'enable_BMM350': False, 'disable_BMM350': False, 'enable_CompassLearn': False, 'disable_CompassLearn': False, 'enable_DRONECAN_COMPASS': False, 'disable_DRONECAN_COMPASS': False, 'enable_DRONECAN_COMPASS_HIRES': False, 'disable_DRONECAN_COMPASS_HIRES': False, 'enable_EXTERNALAHRS_COMPASS': False, 'disable_EXTERNALAHRS_COMPASS': False, 'enable_FixedYawCal': False, 'disable_FixedYawCal': False, 'enable_HMC5843': False, 'disable_HMC5843': False, 'enable_ICM20948': False, 'disable_ICM20948': False, 'enable_IIS2MDC': False, 'disable_IIS2MDC': False, 'enable_IST8308': False, 'disable_IST8308': False, 'enable_IST8310': False, 'disable_IST8310': False, 'enable_LIS3MDL': False, 'disable_LIS3MDL': False, 'enable_LSM303D': False, 'disable_LSM303D': False, 'enable_LSM9DS1': False, 'disable_LSM9DS1': False, 'enable_MAG3110': False, 'disable_MAG3110': False, 'enable_MMC3416': False, 'disable_MMC3416': False, 'enable_MMC5XX3': False, 'disable_MMC5XX3': False, 'enable_QMC5883L': False, 'disable_QMC5883L': False, 'enable_QMC5883P': False, 'disable_QMC5883P': False, 'enable_RM3100': False, 'disable_RM3100': False, 'enable_AC_PAYLOAD_PLACE_ENABLED': False, 'disable_AC_PAYLOAD_PLACE_ENABLED': False, 'enable_COPTER_ADVANCED_FAILSAFE': False, 'disable_COPTER_ADVANCED_FAILSAFE': False, 'enable_COPTER_AHRS_AUTO_TRIM': False, 'disable_COPTER_AHRS_AUTO_TRIM': False, 'enable_MODE_BRAKE': False, 'disable_MODE_BRAKE': False, 'enable_MODE_FLIP': False, 'disable_MODE_FLIP': False, 'enable_MODE_FLOWHOLD': False, 'disable_MODE_FLOWHOLD': False, 'enable_MODE_FOLLOW': False, 'disable_MODE_FOLLOW': False, 'enable_MODE_GUIDED_NOGPS': False, 'disable_MODE_GUIDED_NOGPS': False, 'enable_MODE_SPORT': False, 'disable_MODE_SPORT': False, 'enable_MODE_SYSTEMID': False, 'disable_MODE_SYSTEMID': False, 'enable_MODE_TURTLE': False, 'disable_MODE_TURTLE': False, 'enable_MODE_ZIGZAG': False, 'disable_MODE_ZIGZAG': False, 'enable_DDS': False, 'disable_DDS': False, 'enable_CRASHCATCHER': False, 'disable_CRASHCATCHER': False, 'enable_KILL_IMU': False, 'disable_KILL_IMU': False, 'enable_ESC_EXTENDED_TELM': False, 'disable_ESC_EXTENDED_TELM': False, 'enable_PICCOLOCAN': False, 'disable_PICCOLOCAN': False, 'enable_TORQEEDO': False, 'disable_TORQEEDO': False, 'enable_APJ_TOOL_PARAMETERS': False, 'disable_APJ_TOOL_PARAMETERS': False, 'enable_FILESYSTEM_MISSION': False, 'disable_FILESYSTEM_MISSION': False, 'enable_FILESYSTEM_PARAM': False, 'disable_FILESYSTEM_PARAM': False, 'enable_FILESYSTEM_ROMFS': False, 'disable_FILESYSTEM_ROMFS': False, 'enable_FILESYSTEM_SYS': False, 'disable_FILESYSTEM_SYS': False, 'enable_DroneCAN_GPS_Out': False, 'disable_DroneCAN_GPS_Out': False, 'enable_ERB': False, 'disable_ERB': False, 'enable_GPS_Blending': False, 'disable_GPS_Blending': False, 'enable_GSOF': False, 'disable_GSOF': False, 'enable_MAV': False, 'disable_MAV': False, 'enable_NMEA_GPS': False, 'disable_NMEA_GPS': False, 'enable_NMEA_UNICORE': False, 'disable_NMEA_UNICORE': False, 'enable_NOVA': False, 'disable_NOVA': False, 'enable_SBF': False, 'disable_SBF': False, 'enable_SBP': False, 'disable_SBP': False, 'enable_SBP2': False, 'disable_SBP2': False, 'enable_SIRF': False, 'disable_SIRF': False, 'enable_UBLOX': False, 'disable_UBLOX': False, 'enable_GENERATOR': False, 'disable_GENERATOR': False, 'enable_GENERATOR_IE2400': False, 'disable_GENERATOR_IE2400': False, 'enable_GENERATOR_IE650': False, 'disable_GENERATOR_IE650': False, 'enable_GENERATOR_LOWEHEISER': False, 'disable_GENERATOR_LOWEHEISER': False, 'enable_GENERATOR_RICHENPOWER': False, 'disable_GENERATOR_RICHENPOWER': False, 'enable_ALEXMOS': False, 'disable_ALEXMOS': False, 'enable_CADDX': False, 'disable_CADDX': False, 'enable_GREMSY': False, 'disable_GREMSY': False, 'enable_MOUNT': False, 'disable_MOUNT': False, 'enable_SERVO': False, 'disable_SERVO': False, 'enable_SIYI': False, 'disable_SIYI': False, 'enable_SOLOGIMBAL': False, 'disable_SOLOGIMBAL': False, 'enable_STORM32_MAVLINK': False, 'disable_STORM32_MAVLINK': False, 'enable_STORM32_SERIAL': False, 'disable_STORM32_SERIAL': False, 'enable_TOPOTEK': False, 'disable_TOPOTEK': False, 'enable_VIEWPRO': False, 'disable_VIEWPRO': False, 'enable_XACTI': False, 'disable_XACTI': False, 'enable_XFROBOT': False, 'disable_XFROBOT': False, 'enable_EFI': False, 'disable_EFI': False, 'enable_EFI_CURRAWONGECU': False, 'disable_EFI_CURRAWONGECU': False, 'enable_EFI_DRONECAN': False, 'disable_EFI_DRONECAN': False, 'enable_EFI_HIRTH': False, 'disable_EFI_HIRTH': False, 'enable_EFI_Lutan': False, 'disable_EFI_Lutan': False, 'enable_EFI_MAV': False, 'disable_EFI_MAV': False, 'enable_EFI_MegaSquirt': False, 'disable_EFI_MegaSquirt': False, 'enable_EFI_NMPWU': False, 'disable_EFI_NMPWU': False, 'enable_ICE_Engine': False, 'disable_ICE_Engine': False, 'enable_BatchSampler': False, 'disable_BatchSampler': False, 'enable_HarmonicNotches': False, 'disable_HarmonicNotches': False, 'enable_TEMPCAL': False, 'disable_TEMPCAL': False, 'enable_ADSB': False, 'disable_ADSB': False, 'enable_ADSB_SAGETECH': False, 'disable_ADSB_SAGETECH': False, 'enable_ADSB_SAGETECH_MXS': False, 'disable_ADSB_SAGETECH_MXS': False, 'enable_ADSB_UAVIONIX': False, 'disable_ADSB_UAVIONIX': False, 'enable_ADSB_UAVIONX_UCP': False, 'disable_ADSB_UAVIONX_UCP': False, 'enable_AIS': False, 'disable_AIS': False, 'enable_OpenDroneID': False, 'disable_OpenDroneID': False, 'enable_AP_MAVLINK_FTP_ENABLED': False, 'disable_AP_MAVLINK_FTP_ENABLED': False, 'enable_FENCEPOINT_PROTOCOL': False, 'disable_FENCEPOINT_PROTOCOL': False, 'enable_HIGHLAT2': False, 'disable_HIGHLAT2': False, 'enable_MAVLINK_MSG_FLIGHT_INFORMATION': False, 'disable_MAVLINK_MSG_FLIGHT_INFORMATION': False, 'enable_MAVLINK_MSG_MISSION_REQUEST': False, 'disable_MAVLINK_MSG_MISSION_REQUEST': False, 'enable_MAVLINK_MSG_RC_CHANNELS_RAW': False, 'disable_MAVLINK_MSG_RC_CHANNELS_RAW': False, 'enable_MAVLINK_VERSION_REQUEST': False, 'disable_MAVLINK_VERSION_REQUEST': False, 'enable_MAV_CMD_SET_HAGL': False, 'disable_MAV_CMD_SET_HAGL': False, 'enable_MAV_DEVICE_OP': False, 'disable_MAV_DEVICE_OP': False, 'enable_MAV_MSG_RELAY_STATUS': False, 'disable_MAV_MSG_RELAY_STATUS': False, 'enable_MAV_MSG_SERIAL_CONTROL': False, 'disable_MAV_MSG_SERIAL_CONTROL': False, 'enable_MAV_SERVO_RELAY': False, 'disable_MAV_SERVO_RELAY': False, 'enable_RALLYPOINT_PROTOCOL': False, 'disable_RALLYPOINT_PROTOCOL': False, 'enable_RANGEFINDER_SENDING': False, 'disable_RANGEFINDER_SENDING': False, 'enable_REQUEST_AUTOPILOT_CAPA': False, 'disable_REQUEST_AUTOPILOT_CAPA': False, 'enable_VIDEO_STREAM_INFORMATION': False, 'disable_VIDEO_STREAM_INFORMATION': False, 'enable_DISPLAYPORT_INAV_FONTS': False, 'disable_DISPLAYPORT_INAV_FONTS': False, 'enable_MSP': False, 'disable_MSP': False, 'enable_MSP_COMPASS': False, 'disable_MSP_COMPASS': False, 'enable_MSP_DISPLAYPORT': False, 'disable_MSP_DISPLAYPORT': False, 'enable_MSP_GPS': False, 'disable_MSP_GPS': False, 'enable_MSP_OPTICALFLOW': False, 'disable_MSP_OPTICALFLOW': False, 'enable_MSP_RANGEFINDER': False, 'disable_MSP_RANGEFINDER': False, 'enable_MSP_SENSORS': False, 'disable_MSP_SENSORS': False, 'enable_MISSION_NAV_PAYLOAD_PLACE': False, 'disable_MISSION_NAV_PAYLOAD_PLACE': False, 'enable_CAN_MCAST': False, 'disable_CAN_MCAST': False, 'enable_Networking': False, 'disable_Networking': False, 'enable_PPP': False, 'disable_PPP': False, 'enable_DISPLAY': False, 'disable_DISPLAY': False, 'enable_LED_CONTROL': False, 'disable_LED_CONTROL': False, 'enable_NOTIFY_NCP5623': False, 'disable_NOTIFY_NCP5623': False, 'enable_NOTIFY_NEOPIXEL': False, 'disable_NOTIFY_NEOPIXEL': False, 'enable_NOTIFY_PROFILED': False, 'disable_NOTIFY_PROFILED': False, 'enable_NOTIFY_PROFILED_SPI': False, 'disable_NOTIFY_PROFILED_SPI': False, 'enable_PLAY_TUNE': False, 'disable_PLAY_TUNE': False, 'enable_TONEALARM': False, 'disable_TONEALARM': False, 'enable_OSD': False, 'disable_OSD': False, 'enable_OSD_EXTENDED_LINK_STATS': False, 'disable_OSD_EXTENDED_LINK_STATS': False, 'enable_OSD_PARAM': False, 'disable_OSD_PARAM': False, 'enable_OSD_SIDEBARS': False, 'disable_OSD_SIDEBARS': False, 'enable_PLUSCODE': False, 'disable_PLUSCODE': False, 'enable_AP_FOLLOW': False, 'disable_AP_FOLLOW': False, 'enable_BOOTLOADER_FLASHING': False, 'disable_BOOTLOADER_FLASHING': False, 'enable_Buttons': False, 'disable_Buttons': False, 'enable_COMPASS_CAL': False, 'disable_COMPASS_CAL': False, 'enable_CUSTOM_ROTATIONS': False, 'disable_CUSTOM_ROTATIONS': False, 'enable_DRONECAN_SERIAL': False, 'disable_DRONECAN_SERIAL': False, 'enable_GyroFFT': False, 'disable_GyroFFT': False, 'enable_Logging': False, 'disable_Logging': False, 'enable_NMEA_OUTPUT': False, 'disable_NMEA_OUTPUT': False, 'enable_PID_FILTERING': False, 'disable_PID_FILTERING': False, 'enable_RateLoopThread': False, 'disable_RateLoopThread': False, 'enable_SCRIPTING': False, 'disable_SCRIPTING': False, 'enable_SCRIPTING_SERIALDEVICE': False, 'disable_SCRIPTING_SERIALDEVICE': False, 'enable_SDCARD_FORMATTING': False, 'disable_SDCARD_FORMATTING': False, 'enable_SDCARD_MISSION': False, 'disable_SDCARD_MISSION': False, 'enable_SERIALDEVICE_REGISTER': False, 'disable_SERIALDEVICE_REGISTER': False, 'enable_SLCAN': False, 'disable_SLCAN': False, 'enable_GRIPPER': False, 'disable_GRIPPER': False, 'enable_LANDING_GEAR': False, 'disable_LANDING_GEAR': False, 'enable_RELAY': False, 'disable_RELAY': False, 'enable_SERVORELAY_EVENTS': False, 'disable_SERVORELAY_EVENTS': False, 'enable_SPRAYER': False, 'disable_SPRAYER': False, 'enable_WINCH': False, 'disable_WINCH': False, 'enable_WINCH_DAIWA': False, 'disable_WINCH_DAIWA': False, 'enable_WINCH_PWM': False, 'disable_WINCH_PWM': False, 'enable_ADVANCED_FAILSAFE': False, 'disable_ADVANCED_FAILSAFE': False, 'enable_AP_TX_TUNING': False, 'disable_AP_TX_TUNING': False, 'enable_AUTOLAND_MODE': False, 'disable_AUTOLAND_MODE': False, 'enable_DEEPSTALL': False, 'disable_DEEPSTALL': False, 'enable_PLANE_BLACKBOX': False, 'disable_PLANE_BLACKBOX': False, 'enable_PLANE_GLIDER_PULLUP': False, 'disable_PLANE_GLIDER_PULLUP': False, 'enable_PLANE_GUIDED_SLEW': False, 'disable_PLANE_GUIDED_SLEW': False, 'enable_PLANE_SYSTEMID': False, 'disable_PLANE_SYSTEMID': False, 'enable_QAUTOTUNE': False, 'disable_QAUTOTUNE': False, 'enable_QUADPLANE': False, 'disable_QUADPLANE': False, 'enable_QUICKTUNE': False, 'disable_QUICKTUNE': False, 'enable_SOARING': False, 'disable_SOARING': False, 'enable_PrecLand': False, 'disable_PrecLand': False, 'enable_PrecLand___Companion': False, 'disable_PrecLand___Companion': False, 'enable_PrecLand___IRLock': False, 'disable_PrecLand___IRLock': False, 'enable_PROXIMITY': False, 'disable_PROXIMITY': False, 'enable_PROXIMITY_CYGBOT': False, 'disable_PROXIMITY_CYGBOT': False, 'enable_PROXIMITY_DRONECAN': False, 'disable_PROXIMITY_DRONECAN': False, 'enable_PROXIMITY_HEXSOONRADAR_ENABLED': False, 'disable_PROXIMITY_HEXSOONRADAR_ENABLED': False, 'enable_PROXIMITY_LIGHTWARE_SF40C': False, 'disable_PROXIMITY_LIGHTWARE_SF40C': False, 'enable_PROXIMITY_LIGHTWARE_SF45B': False, 'disable_PROXIMITY_LIGHTWARE_SF45B': False, 'enable_PROXIMITY_MAV': False, 'disable_PROXIMITY_MAV': False, 'enable_PROXIMITY_MR72_ENABLED': False, 'disable_PROXIMITY_MR72_ENABLED': False, 'enable_PROXIMITY_RANGEFINDER': False, 'disable_PROXIMITY_RANGEFINDER': False, 'enable_PROXIMITY_RPLIDARA2': False, 'disable_PROXIMITY_RPLIDARA2': False, 'enable_PROXIMITY_TERRARANGERTOWER': False, 'disable_PROXIMITY_TERRARANGERTOWER': False, 'enable_PROXIMITY_TERRARANGERTOWEREVO': False, 'disable_PROXIMITY_TERRARANGERTOWEREVO': False, 'enable_RC_CRSF': False, 'disable_RC_CRSF': False, 'enable_RC_GHST': False, 'disable_RC_GHST': False, 'enable_RC_IBUS': False, 'disable_RC_IBUS': False, 'enable_RC_MAVLINK_RADIO': False, 'disable_RC_MAVLINK_RADIO': False, 'enable_RC_PPMSUM': False, 'disable_RC_PPMSUM': False, 'enable_RC_Protocol': False, 'disable_RC_Protocol': False, 'enable_RC_SBUS': False, 'disable_RC_SBUS': False, 'enable_RC_SRXL': False, 'disable_RC_SRXL': False, 'enable_RC_SRXL2': False, 'disable_RC_SRXL2': False, 'enable_RC_ST24': False, 'disable_RC_ST24': False, 'enable_RC_SUMD': False, 'disable_RC_SUMD': False, 'enable_RSSI': False, 'disable_RSSI': False, 'enable_RANGEFINDER': False, 'disable_RANGEFINDER': False, 'enable_RANGEFINDER_ANALOG': False, 'disable_RANGEFINDER_ANALOG': False, 'enable_RANGEFINDER_BENEWAKE_CAN': False, 'disable_RANGEFINDER_BENEWAKE_CAN': False, 'enable_RANGEFINDER_BENEWAKE_TF02': False, 'disable_RANGEFINDER_BENEWAKE_TF02': False, 'enable_RANGEFINDER_BENEWAKE_TF03': False, 'disable_RANGEFINDER_BENEWAKE_TF03': False, 'enable_RANGEFINDER_BLPING': False, 'disable_RANGEFINDER_BLPING': False, 'enable_RANGEFINDER_DRONECAN': False, 'disable_RANGEFINDER_DRONECAN': False, 'enable_RANGEFINDER_GYUS42V2': False, 'disable_RANGEFINDER_GYUS42V2': False, 'enable_RANGEFINDER_HC_SR04': False, 'disable_RANGEFINDER_HC_SR04': False, 'enable_RANGEFINDER_HEXSOONRADAR': False, 'disable_RANGEFINDER_HEXSOONRADAR': False, 'enable_RANGEFINDER_JRE_SERIAL': False, 'disable_RANGEFINDER_JRE_SERIAL': False, 'enable_RANGEFINDER_LANBAO': False, 'disable_RANGEFINDER_LANBAO': False, 'enable_RANGEFINDER_LEDDARONE': False, 'disable_RANGEFINDER_LEDDARONE': False, 'enable_RANGEFINDER_LEDDARVU8': False, 'disable_RANGEFINDER_LEDDARVU8': False, 'enable_RANGEFINDER_LIGHTWARE_SERIAL': False, 'disable_RANGEFINDER_LIGHTWARE_SERIAL': False, 'enable_RANGEFINDER_LUA': False, 'disable_RANGEFINDER_LUA': False, 'enable_RANGEFINDER_LWI2C': False, 'disable_RANGEFINDER_LWI2C': False, 'enable_RANGEFINDER_MAVLINK': False, 'disable_RANGEFINDER_MAVLINK': False, 'enable_RANGEFINDER_MAXBOTIX_SERIAL': False, 'disable_RANGEFINDER_MAXBOTIX_SERIAL': False, 'enable_RANGEFINDER_MAXSONARI2CXL': False, 'disable_RANGEFINDER_MAXSONARI2CXL': False, 'enable_RANGEFINDER_NMEA': False, 'disable_RANGEFINDER_NMEA': False, 'enable_RANGEFINDER_NOOPLOOP': False, 'disable_RANGEFINDER_NOOPLOOP': False, 'enable_RANGEFINDER_NRA24_CAN': False, 'disable_RANGEFINDER_NRA24_CAN': False, 'enable_RANGEFINDER_PULSEDLIGHTLRF': False, 'disable_RANGEFINDER_PULSEDLIGHTLRF': False, 'enable_RANGEFINDER_PWM': False, 'disable_RANGEFINDER_PWM': False, 'enable_RANGEFINDER_RDS02UF': False, 'disable_RANGEFINDER_RDS02UF': False, 'enable_RANGEFINDER_TOFSF_I2C': False, 'disable_RANGEFINDER_TOFSF_I2C': False, 'enable_RANGEFINDER_TOFSP_CAN': False, 'disable_RANGEFINDER_TOFSP_CAN': False, 'enable_RANGEFINDER_TRI2C': False, 'disable_RANGEFINDER_TRI2C': False, 'enable_RANGEFINDER_TR_SERIAL': False, 'disable_RANGEFINDER_TR_SERIAL': False, 'enable_RANGEFINDER_USD1_CAN': False, 'disable_RANGEFINDER_USD1_CAN': False, 'enable_RANGEFINDER_USD1_SERIAL': False, 'disable_RANGEFINDER_USD1_SERIAL': False, 'enable_RANGEFINDER_VL53L0X': False, 'disable_RANGEFINDER_VL53L0X': False, 'enable_RANGEFINDER_VL53L1X': False, 'disable_RANGEFINDER_VL53L1X': False, 'enable_RANGEFINDER_WASP': False, 'disable_RANGEFINDER_WASP': False, 'enable_RFND_BENEWAKE_TFMINI': False, 'disable_RFND_BENEWAKE_TFMINI': False, 'enable_RFND_BENEWAKE_TFMINIPLUS': False, 'disable_RFND_BENEWAKE_TFMINIPLUS': False, 'enable_ROVER_ADVANCED_FAILSAFE': False, 'disable_ROVER_ADVANCED_FAILSAFE': False, 'enable_ROVER_AUTO_ARM_ONCE': False, 'disable_ROVER_AUTO_ARM_ONCE': False, 'enable_AC_AVOID': False, 'disable_AC_AVOID': False, 'enable_AC_OAPATHPLANNER': False, 'disable_AC_OAPATHPLANNER': False, 'enable_FENCE': False, 'disable_FENCE': False, 'enable_PARACHUTE': False, 'disable_PARACHUTE': False, 'enable_RALLY': False, 'disable_RALLY': False, 'enable_AIRSPEED': False, 'disable_AIRSPEED': False, 'enable_BEACON': False, 'disable_BEACON': False, 'enable_GPS_MOVING_BASELINE': False, 'disable_GPS_MOVING_BASELINE': False, 'enable_IMU_ON_UART': False, 'disable_IMU_ON_UART': False, 'enable_OPTICALFLOW': False, 'disable_OPTICALFLOW': False, 'enable_OPTICALFLOW_CXOF': False, 'disable_OPTICALFLOW_CXOF': False, 'enable_OPTICALFLOW_HEREFLOW': False, 'disable_OPTICALFLOW_HEREFLOW': False, 'enable_OPTICALFLOW_MAV': False, 'disable_OPTICALFLOW_MAV': False, 'enable_OPTICALFLOW_ONBOARD': False, 'disable_OPTICALFLOW_ONBOARD': False, 'enable_OPTICALFLOW_PIXART': False, 'disable_OPTICALFLOW_PIXART': False, 'enable_OPTICALFLOW_PX4FLOW': False, 'disable_OPTICALFLOW_PX4FLOW': False, 'enable_OPTICALFLOW_UPFLOW': False, 'disable_OPTICALFLOW_UPFLOW': False, 'enable_RPM': False, 'disable_RPM': False, 'enable_RPM_DRONECAN': False, 'disable_RPM_DRONECAN': False, 'enable_RPM_EFI': False, 'disable_RPM_EFI': False, 'enable_RPM_ESC_TELEM': False, 'disable_RPM_ESC_TELEM': False, 'enable_RPM_GENERATOR': False, 'disable_RPM_GENERATOR': False, 'enable_RPM_HARMONIC_NOTCH': False, 'disable_RPM_HARMONIC_NOTCH': False, 'enable_RPM_PIN': False, 'disable_RPM_PIN': False, 'enable_TEMP': False, 'disable_TEMP': False, 'enable_TEMP_MCP9600': False, 'disable_TEMP_MCP9600': False, 'enable_TEMP_MLX90614': False, 'disable_TEMP_MLX90614': False, 'enable_TEMP_SHT3X': False, 'disable_TEMP_SHT3X': False, 'enable_TEMP_TSYS01': False, 'disable_TEMP_TSYS01': False, 'enable_TEMP_TSYS03': False, 'disable_TEMP_TSYS03': False, 'enable_AUX_FUNCTION_STRINGS': False, 'disable_AUX_FUNCTION_STRINGS': False, 'enable_Bidirectional_FrSky_Telemetry': False, 'disable_Bidirectional_FrSky_Telemetry': False, 'enable_CRSF': False, 'disable_CRSF': False, 'enable_CRSF_Scripting': False, 'disable_CRSF_Scripting': False, 'enable_CRSFText': False, 'disable_CRSFText': False, 'enable_FrSky': False, 'disable_FrSky': False, 'enable_FrSky_D': False, 'disable_FrSky_D': False, 'enable_FrSky_SPort': False, 'disable_FrSky_SPort': False, 'enable_FrSky_SPort_PassThrough': False, 'disable_FrSky_SPort_PassThrough': False, 'enable_GHST': False, 'disable_GHST': False, 'enable_HOTT': False, 'disable_HOTT': False, 'enable_LTM': False, 'disable_LTM': False, 'enable_SPEKTRUM': False, 'disable_SPEKTRUM': False, 'enable_i_BUS': False, 'disable_i_BUS': False, 'enable_DECA': False, 'disable_DECA': False, 'enable_DODECAHEXA': False, 'disable_DODECAHEXA': False, 'enable_HEXA': False, 'disable_HEXA': False, 'enable_OCTA': False, 'disable_OCTA': False, 'enable_OCTAQUAD': False, 'disable_OCTAQUAD': False, 'enable_QUAD': False, 'disable_QUAD': False, 'enable_Y6': False, 'disable_Y6': False, 'enable_SMARTAUDIO': False, 'disable_SMARTAUDIO': False, 'enable_TRAMP': False, 'disable_TRAMP': False, 'enable_VIDEO_TX': False, 'disable_VIDEO_TX': False, 'embed_BattMon_ANX': False, 'embed_EFI_DLA': False, 'embed_EFI_Halo6000': False, 'embed_EFI_HFE': False, 'embed_EFI_NMEA2k': False, 'embed_EFI_SkyPower': False, 'embed_Generator_SVFFI': False, 'embed_Hobbywing_DataLink': False, 'embed_INF_Inject': False, 'embed_LTE_modem': False, 'embed_mount_djirs2_driver': False, 'embed_mount_viewpro_driver': False, 'embed_torqeedo_torqlink': False, 'embed_UltraMotion': False, 'embed_advance_wp': False, 'embed_ahrs_set_origin': False, 'embed_ahrs_source_extnav_optflow': False, 'embed_BatteryTag': False, 'embed_BattEstimate': False, 'embed_camera_change_setting': False, 'embed_copter_deadreckon_home': False, 'embed_copter_slung_payload': False, 'embed_copter_terrain_brake': False, 'embed_crsf_calibrate': False, 'embed_follow_target_send': False, 'embed_forward_flight_motor_shutdown': False, 'embed_Gimbal_Camera_Mode': False, 'embed_Heli_idle_control': False, 'embed_Heli_IM_COL_Tune': False, 'embed_Hexsoon LEDs': False, 'embed_leds_on_a_switch': False, 'embed_MissionSelector': False, 'embed_motor_failure_test': False, 'embed_mount_poi': False, 'embed_net_webserver': False, 'embed_ONVIF_Camera_Control': False, 'embed_param_set': False, 'embed_Param_Controller': False, 'embed_pelco_d_antennatracker': False, 'embed_plane_package_place': False, 'embed_plane_precland': False, 'embed_plane_ship_landing': False, 'embed_QuadPlane_Low_Alt_FW_mode_prevention': False, 'embed_repl': False, 'embed_revert_param': False, 'embed_RockBlock': False, 'embed_rover_quicktune': False, 'embed_runcam_on_arm': False, 'embed_Script_Controller': False, 'embed_SmartAudio': False, 'embed_UniversalAutoLand': False, 'embed_video_stream_information': False, 'embed_VTOL_quicktune': False, 'embed_winch_control': False, 'embed_x_quad_cg_allocation': False}
out_dir = '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot/build'
run_dir = '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot'
top_dir = '/cygdrive/c/Users/<USER>/Desktop/Teknofest 2/ardupilot'
