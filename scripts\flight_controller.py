"""
Uçuş kontrol sınıfı - AirSim ile drone kontrolü
"""

import sys
import os
import time
import threading
from typing import Optional, Tuple

# AirSim path'ini ekle
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'AirSim', 'PythonClient'))
import airsim

class FlightController:
    """AirSim drone kontrol sınıfı"""
    
    def __init__(self, vehicle_name: str = ""):
        self.vehicle_name = vehicle_name
        self.client: Optional[airsim.MultirotorClient] = None
        self.is_connected = False
        self.is_armed = False
        self.is_flying = False
        self.manual_mode = False
        
        # Kontrol değerleri
        self.current_rc_data = airsim.RCData()
        self.current_rc_data.is_initialized = True
        self.current_rc_data.is_valid = True
        
        # Güvenlik
        self.emergency_stop = False
        self.last_command_time = time.time()
        self.command_timeout = 2.0  # 2 saniye timeout
        
        # Thread güvenliği
        self.lock = threading.Lock()
        
    def connect(self) -> bool:
        """AirSim'e bağlan"""
        try:
            print("AirSim'e bağlanıyor...")
            self.client = airsim.MultirotorClient()
            self.client.confirmConnection()
            print("✓ AirSim bağlantısı başarılı")
            self.is_connected = True
            return True
        except Exception as e:
            print(f"✗ AirSim bağlantı hatası: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Bağlantıyı kes"""
        if self.is_connected and self.client:
            try:
                if self.is_armed:
                    self.disarm()
                self.client = None
                self.is_connected = False
                print("✓ AirSim bağlantısı kesildi")
            except Exception as e:
                print(f"✗ Bağlantı kesme hatası: {e}")
    
    def arm(self) -> bool:
        """Drone'u arm et"""
        if not self.is_connected or not self.client:
            print("✗ AirSim'e bağlı değil")
            return False
        
        try:
            print("Drone arm ediliyor...")
            self.client.enableApiControl(True, self.vehicle_name)
            self.client.armDisarm(True, self.vehicle_name)
            self.is_armed = True
            print("✓ Drone arm edildi")
            return True
        except Exception as e:
            print(f"✗ Arm etme hatası: {e}")
            return False
    
    def disarm(self) -> bool:
        """Drone'u disarm et"""
        if not self.is_connected or not self.client:
            return False
        
        try:
            print("Drone disarm ediliyor...")
            self.client.armDisarm(False, self.vehicle_name)
            self.client.enableApiControl(False, self.vehicle_name)
            self.is_armed = False
            self.is_flying = False
            self.manual_mode = False
            print("✓ Drone disarm edildi")
            return True
        except Exception as e:
            print(f"✗ Disarm etme hatası: {e}")
            return False
    
    def takeoff(self) -> bool:
        """Kalkış yap"""
        if not self.is_armed:
            print("✗ Drone arm edilmemiş")
            return False
        
        try:
            print("Kalkış yapılıyor...")
            self.client.takeoffAsync(vehicle_name=self.vehicle_name).join()
            self.is_flying = True
            print("✓ Kalkış tamamlandı")
            return True
        except Exception as e:
            print(f"✗ Kalkış hatası: {e}")
            return False
    
    def land(self) -> bool:
        """İniş yap"""
        if not self.is_flying:
            return True
        
        try:
            print("İniş yapılıyor...")
            self.client.landAsync(vehicle_name=self.vehicle_name).join()
            self.is_flying = False
            self.manual_mode = False
            print("✓ İniş tamamlandı")
            return True
        except Exception as e:
            print(f"✗ İniş hatası: {e}")
            return False
    
    def enable_manual_mode(self) -> bool:
        """Manuel kontrol modunu etkinleştir"""
        if not self.is_armed:
            print("✗ Drone arm edilmemiş")
            return False
        
        try:
            print("Manuel mod etkinleştiriliyor...")
            # Manuel mod için büyük değerler kullan (sınırsız hareket için)
            future = self.client.moveByManualAsync(
                vx_max=1e6, vy_max=1e6, z_min=-1e6, 
                duration=1e10, vehicle_name=self.vehicle_name
            )
            self.manual_mode = True
            print("✓ Manuel mod etkinleştirildi")
            return True
        except Exception as e:
            print(f"✗ Manuel mod hatası: {e}")
            return False
    
    def send_rc_command(self, pitch: float, roll: float, throttle: float, yaw: float):
        """RC komutunu gönder"""
        if not self.manual_mode or not self.client:
            return
        
        with self.lock:
            try:
                # RC data güncelle
                self.current_rc_data.pitch = pitch
                self.current_rc_data.roll = roll
                self.current_rc_data.throttle = throttle
                self.current_rc_data.yaw = yaw
                self.current_rc_data.timestamp = int(time.time() * 1000000)  # microseconds
                
                # Komutu gönder
                self.client.moveByRC(self.current_rc_data, self.vehicle_name)
                self.last_command_time = time.time()
                
            except Exception as e:
                print(f"✗ RC komut hatası: {e}")
    
    def emergency_stop(self):
        """Acil durum - hover moduna geç"""
        if self.client and self.is_flying:
            try:
                print("🚨 ACİL DURUM - Hover moduna geçiliyor...")
                self.client.hoverAsync(vehicle_name=self.vehicle_name)
                self.manual_mode = False
                self.emergency_stop = True
            except Exception as e:
                print(f"✗ Acil durum hatası: {e}")
    
    def get_state(self) -> dict:
        """Drone durumunu al"""
        if not self.client:
            return {}
        
        try:
            state = self.client.getMultirotorState(vehicle_name=self.vehicle_name)
            return {
                'position': {
                    'x': state.kinematics_estimated.position.x_val,
                    'y': state.kinematics_estimated.position.y_val, 
                    'z': state.kinematics_estimated.position.z_val
                },
                'velocity': {
                    'x': state.kinematics_estimated.linear_velocity.x_val,
                    'y': state.kinematics_estimated.linear_velocity.y_val,
                    'z': state.kinematics_estimated.linear_velocity.z_val
                },
                'orientation': {
                    'w': state.kinematics_estimated.orientation.w_val,
                    'x': state.kinematics_estimated.orientation.x_val,
                    'y': state.kinematics_estimated.orientation.y_val,
                    'z': state.kinematics_estimated.orientation.z_val
                },
                'armed': self.is_armed,
                'flying': self.is_flying,
                'manual_mode': self.manual_mode,
                'landed_state': state.landed_state
            }
        except Exception as e:
            print(f"✗ Durum alma hatası: {e}")
            return {}
    
    def is_command_timeout(self) -> bool:
        """Komut timeout kontrolü"""
        return (time.time() - self.last_command_time) > self.command_timeout
