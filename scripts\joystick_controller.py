"""
AirSim Joystick/Gamepad Kontrol Sistemi
Bu script joystick/gamepad ile AirSim'deki drone'u kontrol eder
"""

import pygame
import sys
import time
import threading
from typing import Optional

from joystick_config import JoystickConfig
from flight_controller import FlightController

class JoystickController:
    """Joystick kontrol sınıfı"""
    
    def __init__(self):
        self.config = JoystickConfig()
        self.flight_controller = FlightController()
        self.joystick: Optional[pygame.joystick.Joystick] = None
        self.running = False
        
        # Buton durumları (basılı tutma kontrolü için)
        self.button_states = {}
        self.last_button_press = {}
        self.button_debounce_time = 0.3  # 300ms debounce
        
        # Display thread
        self.display_thread = None
        
    def initialize_pygame(self) -> bool:
        """Pygame'i başlat"""
        try:
            pygame.init()
            pygame.joystick.init()
            print("✓ Pygame başlatıldı")
            return True
        except Exception as e:
            print(f"✗ Pygame başlatma hatası: {e}")
            return False
    
    def find_joystick(self) -> bool:
        """Joystick/gamepad bul ve bağlan"""
        joystick_count = pygame.joystick.get_count()
        
        if joystick_count == 0:
            print("✗ Hiç joystick/gamepad bulunamadı!")
            print("  Lütfen bir gamepad bağlayın ve tekrar deneyin.")
            return False
        
        print(f"✓ {joystick_count} joystick/gamepad bulundu:")
        
        # İlk joystick'i kullan
        self.joystick = pygame.joystick.Joystick(0)
        self.joystick.init()
        
        print(f"  - {self.joystick.get_name()}")
        print(f"  - {self.joystick.get_numaxes()} axis")
        print(f"  - {self.joystick.get_numbuttons()} buton")
        print(f"  - {self.joystick.get_numhats()} hat")
        
        return True
    
    def is_button_pressed(self, button_id: int) -> bool:
        """Buton basılma kontrolü (debounce ile)"""
        current_time = time.time()
        
        if button_id not in self.last_button_press:
            self.last_button_press[button_id] = 0
        
        if (current_time - self.last_button_press[button_id]) > self.button_debounce_time:
            if self.joystick.get_button(button_id):
                self.last_button_press[button_id] = current_time
                return True
        
        return False
    
    def handle_buttons(self):
        """Buton işlemlerini yönet"""
        try:
            # ARM/DISARM
            if self.is_button_pressed(self.config.BUTTON_ARM_DISARM):
                if self.flight_controller.is_armed:
                    self.flight_controller.disarm()
                else:
                    self.flight_controller.arm()
            
            # TAKEOFF
            if self.is_button_pressed(self.config.BUTTON_TAKEOFF):
                if self.flight_controller.is_armed and not self.flight_controller.is_flying:
                    if self.flight_controller.takeoff():
                        self.flight_controller.enable_manual_mode()
            
            # LAND
            if self.is_button_pressed(self.config.BUTTON_LAND):
                if self.flight_controller.is_flying:
                    self.flight_controller.land()
            
            # EMERGENCY STOP
            if self.is_button_pressed(self.config.BUTTON_EMERGENCY):
                self.flight_controller.emergency_stop()
            
            # RESET (Disarm + Reset)
            if self.is_button_pressed(self.config.BUTTON_RESET):
                self.flight_controller.disarm()
                print("🔄 Sistem sıfırlandı")
                
        except Exception as e:
            print(f"✗ Buton işleme hatası: {e}")
    
    def handle_axes(self):
        """Analog stick kontrollerini yönet"""
        if not self.flight_controller.manual_mode:
            return
        
        try:
            # Axis değerlerini al
            pitch_raw = -self.joystick.get_axis(self.config.AXIS_PITCH)  # Ters çevir
            roll_raw = self.joystick.get_axis(self.config.AXIS_ROLL)
            yaw_raw = self.joystick.get_axis(self.config.AXIS_YAW)
            
            # Throttle için trigger kullan (0 ile 1 arası)
            throttle_raw = (self.joystick.get_axis(self.config.AXIS_THROTTLE) + 1) / 2
            
            # Dead zone ve sensitivity uygula
            pitch = self.config.normalize_axis(pitch_raw, self.config.PITCH_SENSITIVITY)
            roll = self.config.normalize_axis(roll_raw, self.config.ROLL_SENSITIVITY)
            yaw = self.config.normalize_axis(yaw_raw, self.config.YAW_SENSITIVITY)
            throttle = max(0, min(1, throttle_raw * self.config.THROTTLE_SENSITIVITY))
            
            # RC komutunu gönder
            self.flight_controller.send_rc_command(pitch, roll, throttle, yaw)
            
        except Exception as e:
            print(f"✗ Axis işleme hatası: {e}")
    
    def display_status(self):
        """Durum bilgilerini göster"""
        while self.running:
            try:
                # Ekranı temizle (Windows için)
                import os
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print("=" * 60)
                print("🎮 AirSim Joystick Controller")
                print("=" * 60)
                
                # Bağlantı durumu
                if self.flight_controller.is_connected:
                    print("✓ AirSim: Bağlı")
                else:
                    print("✗ AirSim: Bağlı değil")
                
                if self.joystick:
                    print(f"✓ Joystick: {self.joystick.get_name()}")
                else:
                    print("✗ Joystick: Bağlı değil")
                
                print("-" * 60)
                
                # Drone durumu
                state = self.flight_controller.get_state()
                if state:
                    print(f"🚁 Drone Durumu:")
                    print(f"   Armed: {'✓' if self.flight_controller.is_armed else '✗'}")
                    print(f"   Flying: {'✓' if self.flight_controller.is_flying else '✗'}")
                    print(f"   Manual Mode: {'✓' if self.flight_controller.manual_mode else '✗'}")
                    
                    pos = state.get('position', {})
                    print(f"   Position: X={pos.get('x', 0):.2f}, Y={pos.get('y', 0):.2f}, Z={pos.get('z', 0):.2f}")
                
                print("-" * 60)
                
                # Joystick durumu
                if self.joystick and self.running:
                    pygame.event.pump()  # Event'leri güncelle
                    
                    print("🎮 Joystick Durumu:")
                    print(f"   Pitch: {-self.joystick.get_axis(self.config.AXIS_PITCH):.2f}")
                    print(f"   Roll:  {self.joystick.get_axis(self.config.AXIS_ROLL):.2f}")
                    print(f"   Yaw:   {self.joystick.get_axis(self.config.AXIS_YAW):.2f}")
                    throttle = (self.joystick.get_axis(self.config.AXIS_THROTTLE) + 1) / 2
                    print(f"   Throttle: {throttle:.2f}")
                
                print("-" * 60)
                print("📋 Kontroller:")
                print("   A: ARM/DISARM    B: TAKEOFF")
                print("   X: LAND          Y: EMERGENCY")
                print("   LB: MODE         RB: RESET")
                print("   Sol Stick: Pitch/Roll")
                print("   Sağ Stick: Yaw")
                print("   Sol Trigger: Throttle")
                print("-" * 60)
                print("❌ Çıkmak için Ctrl+C")
                
                time.sleep(0.5)  # 500ms güncelleme
                
            except Exception as e:
                print(f"✗ Display hatası: {e}")
                time.sleep(1)
    
    def run(self):
        """Ana döngü"""
        print("🚀 AirSim Joystick Controller başlatılıyor...")
        
        # Pygame başlat
        if not self.initialize_pygame():
            return
        
        # Joystick bul
        if not self.find_joystick():
            return
        
        # AirSim'e bağlan
        if not self.flight_controller.connect():
            return
        
        self.running = True
        
        # Display thread'i başlat
        self.display_thread = threading.Thread(target=self.display_status, daemon=True)
        self.display_thread.start()
        
        print("✓ Sistem hazır! Joystick kontrolü aktif.")
        print("  Çıkmak için Ctrl+C tuşlayın.")
        
        try:
            # Ana kontrol döngüsü
            clock = pygame.time.Clock()
            
            while self.running:
                pygame.event.pump()  # Event'leri işle
                
                # Buton kontrolü
                self.handle_buttons()
                
                # Axis kontrolü
                self.handle_axes()
                
                # 50 FPS ile çalış
                clock.tick(50)
                
        except KeyboardInterrupt:
            print("\n🛑 Kullanıcı tarafından durduruldu")
        except Exception as e:
            print(f"\n✗ Beklenmeyen hata: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Temizlik işlemleri"""
        print("\n🧹 Temizlik yapılıyor...")
        self.running = False
        
        if self.flight_controller.is_flying:
            print("  - Drone indiriliyor...")
            self.flight_controller.land()
        
        if self.flight_controller.is_armed:
            print("  - Drone disarm ediliyor...")
            self.flight_controller.disarm()
        
        self.flight_controller.disconnect()
        
        if self.joystick:
            self.joystick.quit()
        
        pygame.quit()
        print("✓ Temizlik tamamlandı")

def main():
    """Ana fonksiyon"""
    controller = JoystickController()
    controller.run()

if __name__ == "__main__":
    main()
