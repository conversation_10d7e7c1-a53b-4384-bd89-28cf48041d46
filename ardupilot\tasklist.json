[{"configure": "3DRControlZeroG", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ACNS-CM4Pilot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ACNS-F405AIO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "aero", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "AeroFox-Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AeroFox-Airspeed-DLVR", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AeroFox-GNSS_F9P", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AEROFOX-H7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "AeroFox-PMU", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AET-H743-Basic", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "airbotf4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "AIRLink", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Airvolute-DCS2", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AnyleafH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Aocoda-RC-H743Dual", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "AR-F407SmartBat", "targets": ["AP_Periph", "bootloader"]}, {"configure": "ARK_CANNODE", "targets": ["AP_Periph", "bootloader"]}, {"configure": "ARK_FPV", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ARK_GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "ARK_RTK_GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "ARKV6X", "targets": ["AP_Periph", "bootloader"]}, {"configure": "AtomRCF405NAVI", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ATOMRCF405NAVI-Deluxe", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "bbbmini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "BeastF7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BeastF7v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BeastH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BeastH7v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "bebop", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "BETAFPV-F405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BETAFPV-F405-I2C", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "bhat", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "BirdCANdy", "targets": ["AP_Periph", "bootloader"]}, {"configure": "BlitzF745", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BlitzF745AIO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BlitzH743Pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BlitzMiniF745", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BlitzWingH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "blue", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "BotBloxDroneNet", "targets": ["AP_Periph", "bootloader"]}, {"configure": "BrahmaF4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BROTHERHOBBYF405v3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "BROTHERHOBBYH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "C-RTK2-HP", "targets": ["AP_Periph", "bootloader"]}, {"configure": "canzero", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "CarbonixF405", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CarbonixL496", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CBU-H7-LC-Stamp", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CBU-H7-St<PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CORVON405V2_1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CORVON743V1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CrazyF405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "crazyflie2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CSKY405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-7-<PERSON><PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-Nora", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-Nora-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-Pixhack-v3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-X7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV-X7-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAV_GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CUAVv5", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAVv5-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAVv5Nano", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CUAVv5Nano-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeBlack", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeBlack+", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeBlack-periph", "targets": ["AP_Periph", "bootloader"]}, {"configure": "<PERSON><PERSON><PERSON><PERSON>-solo", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeNode", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CubeNode-ETH", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CubeOrange", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrange-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrange-joey", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrange-ODID", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrange-periph", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CubeOrange-periph-heavy", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CubeOrange-SimOnHardWare", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrangePlus", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrangePlus-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrangePlus-ODID", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeOrangePlus-SimOnHardWare", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeRedPrimary", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeRedPrimary-PPPGW", "targets": ["AP_Periph", "bootloader"]}, {"configure": "CubeRedSecondary", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeRedSecondary-IO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeSolo", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "CubeYellow-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "dark", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "DevEBoxH7v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "disco", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "DroneerF405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "DrotekP3Pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "<PERSON><PERSON><PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Durandal-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "edge", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "erleboard", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "erlebrain2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "esp32buzz", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32diy", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32empty", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32icarus", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32imu_module_v11", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32nick", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32s3devkit", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32s3empty", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32s3m5stampfly", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "esp32tomte76", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "f103-ADSB", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-HWESC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-QiotekPeriph", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-<PERSON><PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f103-<PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-HWESC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-M10025", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-M10070", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-MatekGPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-PWM", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-TempSensor", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f303-Universal", "targets": ["AP_Periph", "bootloader"]}, {"configure": "F35Lightning", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "f405-MatekAirspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "f405-MatekGPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "F4BY", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "F4BY_F427", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlyingMoonF407", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlyingMoonF427", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlyingMoonH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooF405HD-AIOv2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooF405Pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooF405S-AIO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooF745", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooF745Nano", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FlywooH743Pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "fmuv2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "fmuv3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "fmuv3-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "fmuv5", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FoxeerF405v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FoxeerH743v1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "FreeflyRTK", "targets": ["AP_Periph", "bootloader"]}, {"configure": "G4-ESC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "GEPRC_TAKER_H743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "GEPRCF745BTHD", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "H757I_EVAL", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "H757I_EVAL_intf", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HEEWING-F405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "HEEWING-F405v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Here4AP", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Here4FC", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Hitec-Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HolybroF4_PMU", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HolybroG4_Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HolybroG4_Compass", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HolybroG4_GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "HolybroGPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "IFLIGHT_2RAW_H7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu-dshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu-f103", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu-f103-8MHz-dshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu-f103-dshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "iomcu_f103_8MHz", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JFB100", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JFB110", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JHEM_JHEF405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JHEMCU-GSF405A", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JHEMCU-GSF405A-RX2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JHEMCU-H743HD", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "JHEMCUF405PRO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF4-Wing", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF4Mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF7-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteF7Mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7-Wing", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7Mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7Mini-Nand", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "KakuteH7v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "kha_eth", "targets": ["AP_Periph", "bootloader"]}, {"configure": "linux", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "LongBowF405WING", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "LumenierLUXF765-NDAA", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "luminousbee4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "luminousbee5", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MambaF405-2022", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MambaF405US-I2C", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MambaF405v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MambaH743v4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-CAN", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-STD", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-TE", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-TE-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-Wing", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF405-Wing-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF765-SE", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF765-Wing", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekF765-Wing-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekG474-<PERSON>hot", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekG474-GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekG474-<PERSON><PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekH743-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekH743-per<PERSON>h", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekH7A3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MatekL431-ADSB", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-APDTelem", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-BatteryTag", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-BattMon", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-bdshot", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-<PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-EFI", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-GPS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-H<PERSON><PERSON>elem", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-MagHiRes", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-<PERSON><PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-Proximity", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-Rangefinder", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-RC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MatekL431-<PERSON><PERSON>", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MazzyStar<PERSON>rone", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MFE_AirSpeed_CAN", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MFE_POS3_CAN", "targets": ["AP_Periph", "bootloader"]}, {"configure": "MFT-SEMA100", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MicoAir405Mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MicoAir405v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MicoAir743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MicoAir743-AIO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MicoAir743v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mindpx-v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mini-pix", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "modalai_fc-v1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRo-M10095", "targets": ["AP_Periph", "bootloader"]}, {"configure": "mRoCANPWM-M10126", "targets": ["AP_Periph", "bootloader"]}, {"configure": "mRoControlZeroClassic", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoControlZeroF7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoControlZeroH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoControlZeroH7-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoControlZeroOEMH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoCZeroOEMH7-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoKitCANrevC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "mRoNexus", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoPixracerPro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoPixracerPro-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoX21", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "mRoX21-777", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "MUPilot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "NarinFC-H5", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "NarinFC-H7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "navigator", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "navigator64", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "navio", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "navio2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "Nucleo-G491", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Nucleo-L476", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Nucleo-L496", "targets": ["AP_Periph", "bootloader"]}, {"configure": "NucleoH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "NucleoH755", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "NxtPX4v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "obal", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "ocpoc_zynq", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "omnibusf4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "omnibusf4pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "omnibusf4pro-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "omnibusf4pro-one", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "omnibusf4v6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "OMNIBUSF7V2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "OmnibusNanoV6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "OmnibusNanoV6-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "OrqaF405Pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PH4-mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PH4-mini-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pix32v5", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixC4-<PERSON><PERSON>", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixFlamingo", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixFlamingo-F767", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk1-1M", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk1-1M-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk1-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk4-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk5X", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6C", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6C-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6X", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6X-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6X-ODID", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixhawk6X-PPPGW", "targets": ["AP_Periph", "bootloader"]}, {"configure": "PixPilot-C3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixPilot-V3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixPilot-V6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixPilot-V6PRO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixracer", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixracer-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Pixracer-periph", "targets": ["AP_Periph", "bootloader"]}, {"configure": "PixSurveyA1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixSurveyA1-IND", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "PixSurveyA2-IND", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "pocket", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "pxf", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "pxfmini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "QioTekAdeptF407", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "QioTekZealotF427", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "QioTekZealotH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "QioTekZealotH743-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "QURT", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "R9Pilot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "RadiolinkPIX6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "RADIX2HD", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ReaperF745", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "revo-mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "revo-mini-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "revo-mini-i2c", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "revo-mini-i2c-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "revo-mini-sd", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "rFCU", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "rGNSS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "rst_zynq", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "SDMODELH7V1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SDMODELH7V2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "Sierra-F405", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-F412", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-F9P", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-L431", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-PrecisionPoint", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-TrueNavIC", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-TrueNavPro", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-TrueNavPro-G4", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-TrueNorth", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Sierra-TrueSpeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "sitl", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "replay"]}, {"configure": "SITL_arm_linux_gnueabihf", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "replay"]}, {"configure": "sitl_periph", "targets": ["AP_Periph"]}, {"configure": "sitl_periph_battery_tag", "targets": ["AP_Periph"]}, {"configure": "sitl_periph_battmon", "targets": ["AP_Periph"]}, {"configure": "sitl_periph_gps", "targets": ["AP_Periph"]}, {"configure": "sitl_periph_universal", "targets": ["AP_Periph"]}, {"configure": "SITL_static", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "replay"]}, {"configure": "SITL_x86_64_linux_gnu", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "replay"]}, {"configure": "SIYI_N7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SkySakuraH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SkystarsH7HD", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SkystarsH7HD-bdshot", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "skyviper-f412-rev1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "skyviper-journey", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "skyviper-v2450", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "sparky2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "speedybeef4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SpeedyBeeF405AIO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SpeedyBeeF405Mini", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SpeedyBeeF405WING", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "speedybeef4v3", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "speedybeef4v4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SPRacingH7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SPRacingH7RF", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "StellarF4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "StellarF4V2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "StellarH7V2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SuccexF4", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "SULILGH7-P1-P2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "sw-boom-f407", "targets": ["AP_Periph", "bootloader"]}, {"configure": "sw-nav-f405", "targets": ["AP_Periph", "bootloader"]}, {"configure": "sw-spar-f407", "targets": ["AP_Periph", "bootloader"]}, {"configure": "Swan-K1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "TBS-Colibri-F7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "TBS-L431-Airspeed", "targets": ["AP_Periph", "bootloader"]}, {"configure": "TBS-L431-BattMon", "targets": ["AP_Periph", "bootloader"]}, {"configure": "TBS-L431-<PERSON>urrMon", "targets": ["AP_Periph", "bootloader"]}, {"configure": "TBS-L431-PWM", "targets": ["AP_Periph", "bootloader"]}, {"configure": "TBS_LUCID_H7", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "TBS_LUCID_H7_WING", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "TBS_LUCID_PRO", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "thepeach-k1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "thepeach-r1", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "TMotorH743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "uav-dev-auav-g4", "targets": ["AP_Periph", "bootloader"]}, {"configure": "uav-dev-fc-um982", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "uav-dev-powermodule", "targets": ["AP_Periph", "bootloader"]}, {"configure": "uav-dev_m10s", "targets": ["AP_Periph", "bootloader"]}, {"configure": "VM-L431-BatteryTag", "targets": ["AP_Periph", "bootloader"]}, {"configure": "VM-L431-Periph-Pico", "targets": ["AP_Periph", "bootloader"]}, {"configure": "VM-L431-SRV-Hub-4CHP", "targets": ["AP_Periph", "bootloader"]}, {"configure": "vnav", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}, {"configure": "VRBrain-v51", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "VRBrain-v52", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "VRBrain-v54", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "VRCore-v10", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "VRUBrain-v51", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "VUAV-V7pro", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "X-MAV-AP-H743v2", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "YJUAV_A6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "YJUAV_A6SE", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "YJUAV_A6SE_H743", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "YJUAV_A6Ultra", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ZeroOneX6", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ZeroOneX6_Air", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub", "bootloader"], "buildOptions": "--upload"}, {"configure": "ZubaxGNSS", "targets": ["AP_Periph", "bootloader"]}, {"configure": "zynq", "targets": ["antennatracker", "blimp", "copter", "heli", "plane", "rover", "sub"]}]