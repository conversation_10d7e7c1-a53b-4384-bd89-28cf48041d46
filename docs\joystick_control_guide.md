# AirSim Joystick/Gamepad Kontrol Rehberi

Bu rehber, AirSim simülasyonunda joystick/gamepad ile drone kontrolü için hazırlanmıştır.

## 🎮 Desteklenen Kontrolcüler

- Xbox One/Series Controller
- Xbox 360 Controller  
- PlayStation 4/5 Controller
- Genel USB Gamepad'ler

## 📋 Gereksinimler

### Yazılım Gereksinimleri
- Python 3.7+
- AirSim (Unreal Engine ile)
- Pygame kütüphanesi
- NumPy kütüphanesi

### Donanım Gereksinimleri
- USB Gamepad/Joystick
- AirSim'in çalıştığı bilgisayar

## 🚀 Kurulum

### 1. Sanal Ortamı Aktifleştir
```powershell
venv\Scripts\activate
```

### 2. Gerekli Kütüphaneleri Yükle
```powershell
pip install -r requirements-dev.txt
```

### 3. AirSim'i Başlat
- Unreal Engine'de AirSim projenizi açın
- Simülasyonu başlatın
- Drone'un spawn olduğundan emin olun

## 🎯 Kullanım

### 1. Joystick Testi
Önce joystick'inizin düzgün çalıştığını test edin:
```powershell
python scripts/test_joystick.py
```

### 2. Ana Kontrol Sistemi
Joystick kontrolünü başlatın:
```powershell
python scripts/joystick_controller.py
```

## 🎮 Kontrol Şeması

### Xbox Controller Layout

#### Analog Stickler
- **Sol Stick**: 
  - X ekseni: Roll (sola/sağa yatış)
  - Y ekseni: Pitch (ileri/geri)
- **Sağ Stick**:
  - X ekseni: Yaw (sola/sağa dönüş)
  - Y ekseni: (kullanılmıyor)

#### Tetikler
- **Sol Trigger (LT)**: Throttle (gaz)
- **Sağ Trigger (RT)**: (kullanılmıyor)

#### Butonlar
- **A Butonu**: ARM/DISARM (silahlandır/silahsızlandır)
- **B Butonu**: TAKEOFF (kalkış)
- **X Butonu**: LAND (iniş)
- **Y Butonu**: EMERGENCY STOP (acil durum)
- **LB (Sol Bumper)**: MODE SWITCH (mod değiştir)
- **RB (Sağ Bumper)**: RESET (sıfırla)

## 📊 Durum Göstergeleri

Program çalışırken ekranda şu bilgiler görüntülenir:

### Bağlantı Durumu
- ✓ AirSim: Bağlı / ✗ AirSim: Bağlı değil
- ✓ Joystick: [Controller Adı] / ✗ Joystick: Bağlı değil

### Drone Durumu
- Armed: Silahlandırma durumu
- Flying: Uçuş durumu
- Manual Mode: Manuel kontrol modu
- Position: X, Y, Z koordinatları

### Joystick Durumu
- Pitch, Roll, Yaw, Throttle değerleri
- Gerçek zamanlı analog stick pozisyonları

## 🛡️ Güvenlik Özellikleri

### Otomatik Güvenlik
- **Dead Zone**: Küçük joystick hareketleri yok sayılır
- **Command Timeout**: 2 saniye boyunca komut gelmezse hover moduna geçer
- **Emergency Stop**: Y butonuyla acil durum hover modu
- **Auto Disarm**: Bağlantı kesildiğinde otomatik disarm

### Manuel Güvenlik
- **Reset Butonu**: RB ile sistem sıfırlama
- **Land Butonu**: X ile güvenli iniş
- **Ctrl+C**: Program sonlandırma

## 🔧 Konfigürasyon

### Hassasiyet Ayarları
`scripts/joystick_config.py` dosyasında:
```python
self.PITCH_SENSITIVITY = 1.0    # Pitch hassasiyeti
self.ROLL_SENSITIVITY = 1.0     # Roll hassasiyeti  
self.YAW_SENSITIVITY = 1.0      # Yaw hassasiyeti
self.THROTTLE_SENSITIVITY = 1.0 # Throttle hassasiyeti
```

### Dead Zone Ayarı
```python
self.DEAD_ZONE = 0.1  # 0.0 - 1.0 arası
```

### Buton Eşlemeleri
Farklı controller'lar için buton ID'lerini değiştirebilirsiniz.

## 🐛 Sorun Giderme

### Joystick Tanınmıyor
1. Controller'ın USB ile bağlı olduğundan emin olun
2. Windows'ta "Oyun Denetleyicileri" ayarlarından test edin
3. `test_joystick.py` ile joystick testi yapın

### AirSim Bağlantı Sorunu
1. AirSim'in çalıştığından emin olun
2. Firewall ayarlarını kontrol edin
3. AirSim settings.json dosyasını kontrol edin

### Drone Hareket Etmiyor
1. Drone'un arm edildiğinden emin olun (A butonu)
2. Takeoff yapıldığından emin olun (B butonu)
3. Manuel modun aktif olduğunu kontrol edin

### Performans Sorunları
1. FPS'i düşürün (joystick_controller.py'da clock.tick değeri)
2. Display güncellemesini yavaşlatın
3. Gereksiz debug çıktılarını kapatın

## 📝 Notlar

- İlk kullanımda mutlaka test modunda joystick'i deneyin
- Drone'u ilk kez uçurmadan önce güvenli bir alanda test edin
- Acil durum butonunun yerini öğrenin
- Batarya seviyesini takip edin (AirSim'de sınırsız)

## 🔗 İlgili Dosyalar

- `scripts/joystick_controller.py` - Ana kontrol scripti
- `scripts/flight_controller.py` - Uçuş kontrol sınıfı
- `scripts/joystick_config.py` - Konfigürasyon ayarları
- `scripts/test_joystick.py` - Joystick test scripti

## 📞 Destek

Sorun yaşadığınızda:
1. Önce bu rehberi kontrol edin
2. Test scriptlerini çalıştırın
3. Log çıktılarını inceleyin
4. Gerekirse konfigürasyon dosyalarını düzenleyin
