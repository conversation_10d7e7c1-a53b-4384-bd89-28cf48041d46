"""
Klavye ile AirSim Drone Kontrolü - Demo
Joystick olmadığında test için kullanılabilir
"""

import sys
import time
import threading
from flight_controller import FlightController

class KeyboardDemo:
    """Klavye ile drone kontrolü"""
    
    def __init__(self):
        self.flight_controller = FlightController()
        self.running = False
        
        # Kontrol değerleri
        self.pitch = 0.0
        self.roll = 0.0
        self.throttle = 0.0
        self.yaw = 0.0
        
    def print_help(self):
        """Yardım menüsü"""
        print("\n" + "="*60)
        print("🎮 AirSim Klavye Demo Kontrolleri")
        print("="*60)
        print("Drone Kontrolleri:")
        print("  1: ARM/DISARM")
        print("  2: TAKEOFF")
        print("  3: LAND")
        print("  4: EMERGENCY STOP")
        print("  5: ENABLE MANUAL MODE")
        print()
        print("Hareket Kontrolleri (<PERSON> modda):")
        print("  W/S: Pitch (İleri/Geri)")
        print("  A/D: Roll (Sol/Sağ)")
        print("  Q/E: Yaw (Sola/Sağa dön)")
        print("  R/F: Throttle (Yukarı/Aşağı)")
        print("  SPACE: Tüm kontrolleri sıfırla")
        print()
        print("Diğer:")
        print("  H: Bu yardım menüsünü göster")
        print("  X: Çıkış")
        print("="*60)
    
    def get_keyboard_input(self):
        """Klavye girişini al"""
        try:
            import msvcrt
            if msvcrt.kbhit():
                key = msvcrt.getch().decode('utf-8').lower()
                return key
        except ImportError:
            # Linux/Mac için
            import select
            import sys
            if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
                return sys.stdin.read(1).lower()
        return None
    
    def handle_input(self, key):
        """Klavye girişini işle"""
        if key == '1':
            if self.flight_controller.is_armed:
                self.flight_controller.disarm()
            else:
                self.flight_controller.arm()
        
        elif key == '2':
            if self.flight_controller.is_armed and not self.flight_controller.is_flying:
                if self.flight_controller.takeoff():
                    time.sleep(2)  # Kalkış için bekle
                    self.flight_controller.enable_manual_mode()
        
        elif key == '3':
            if self.flight_controller.is_flying:
                self.flight_controller.land()
        
        elif key == '4':
            self.flight_controller.emergency_stop()
        
        elif key == '5':
            if self.flight_controller.is_armed:
                self.flight_controller.enable_manual_mode()
        
        elif key == 'w':
            self.pitch = min(1.0, self.pitch + 0.1)
        elif key == 's':
            self.pitch = max(-1.0, self.pitch - 0.1)
        
        elif key == 'a':
            self.roll = max(-1.0, self.roll - 0.1)
        elif key == 'd':
            self.roll = min(1.0, self.roll + 0.1)
        
        elif key == 'q':
            self.yaw = max(-1.0, self.yaw - 0.1)
        elif key == 'e':
            self.yaw = min(1.0, self.yaw + 0.1)
        
        elif key == 'r':
            self.throttle = min(1.0, self.throttle + 0.1)
        elif key == 'f':
            self.throttle = max(0.0, self.throttle - 0.1)
        
        elif key == ' ':  # Space
            self.pitch = self.roll = self.yaw = self.throttle = 0.0
            print("🔄 Kontroller sıfırlandı")
        
        elif key == 'h':
            self.print_help()
        
        elif key == 'x':
            print("👋 Çıkış yapılıyor...")
            self.running = False
    
    def display_status(self):
        """Durum gösterimi"""
        while self.running:
            try:
                import os
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print("🎮 AirSim Klavye Demo")
                print("="*60)
                
                # Bağlantı durumu
                if self.flight_controller.is_connected:
                    print("✓ AirSim: Bağlı")
                else:
                    print("✗ AirSim: Bağlı değil")
                
                print("-"*60)
                
                # Drone durumu
                state = self.flight_controller.get_state()
                if state:
                    print("🚁 Drone Durumu:")
                    print(f"   Armed: {'✓' if self.flight_controller.is_armed else '✗'}")
                    print(f"   Flying: {'✓' if self.flight_controller.is_flying else '✗'}")
                    print(f"   Manual Mode: {'✓' if self.flight_controller.manual_mode else '✗'}")
                    
                    pos = state.get('position', {})
                    print(f"   Position: X={pos.get('x', 0):.2f}, Y={pos.get('y', 0):.2f}, Z={pos.get('z', 0):.2f}")
                
                print("-"*60)
                
                # Kontrol değerleri
                print("🎯 Kontrol Değerleri:")
                print(f"   Pitch: {self.pitch:6.2f}")
                print(f"   Roll:  {self.roll:6.2f}")
                print(f"   Yaw:   {self.yaw:6.2f}")
                print(f"   Throttle: {self.throttle:6.2f}")
                
                print("-"*60)
                print("💡 H tuşuna basın - Yardım için")
                print("💡 X tuşuna basın - Çıkış için")
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"✗ Display hatası: {e}")
                time.sleep(1)
    
    def control_loop(self):
        """Kontrol döngüsü"""
        while self.running:
            try:
                # Manuel modda kontrol komutlarını gönder
                if self.flight_controller.manual_mode:
                    self.flight_controller.send_rc_command(
                        self.pitch, self.roll, self.throttle, self.yaw
                    )
                
                time.sleep(0.05)  # 20 Hz
                
            except Exception as e:
                print(f"✗ Kontrol hatası: {e}")
                time.sleep(0.1)
    
    def run(self):
        """Ana döngü"""
        print("🚀 AirSim Klavye Demo başlatılıyor...")
        
        # AirSim'e bağlan
        if not self.flight_controller.connect():
            print("✗ AirSim bağlantısı başarısız!")
            return
        
        self.running = True
        
        # Display thread
        display_thread = threading.Thread(target=self.display_status, daemon=True)
        display_thread.start()
        
        # Control thread
        control_thread = threading.Thread(target=self.control_loop, daemon=True)
        control_thread.start()
        
        self.print_help()
        
        print("\n✓ Demo hazır! Klavye kontrolü aktif.")
        
        try:
            # Ana input döngüsü
            while self.running:
                key = self.get_keyboard_input()
                if key:
                    self.handle_input(key)
                time.sleep(0.01)
                
        except KeyboardInterrupt:
            print("\n🛑 Kullanıcı tarafından durduruldu")
        except Exception as e:
            print(f"\n✗ Beklenmeyen hata: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Temizlik"""
        print("\n🧹 Temizlik yapılıyor...")
        self.running = False
        
        if self.flight_controller.is_flying:
            print("  - Drone indiriliyor...")
            self.flight_controller.land()
        
        if self.flight_controller.is_armed:
            print("  - Drone disarm ediliyor...")
            self.flight_controller.disarm()
        
        self.flight_controller.disconnect()
        print("✓ Temizlik tamamlandı")

def main():
    """Ana fonksiyon"""
    demo = KeyboardDemo()
    demo.run()

if __name__ == "__main__":
    main()
