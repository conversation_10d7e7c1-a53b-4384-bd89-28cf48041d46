import airsim
import numpy as np
import cv2
import time

# AirSim istemcisine bağlan
client = airsim.DroneClient()  # veya CarClient() kullanabilirsin
client.confirmConnection()

# G<PERSON>rü<PERSON><PERSON><PERSON><PERSON> sürekli alıp göster
while True:
    # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al
    responses = client.simGetImages([
        airsim.ImageRequest("0", airsim.ImageType.Scene, False, False)
    ])
    img1d = np.frombuffer(responses[0].image_data_uint8, dtype=np.uint8)

    # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ün boyutlarını al
    img_rgb = img1d.reshape(responses[0].height, responses[0].width, 3)

    # OpenCV ile göster
    cv2.imshow("AirSim Görüntüsü", img_rgb)

    # 'q' tuşuna basılınca çık
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cv2.destroyAllWindows()
