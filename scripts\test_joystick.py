"""
Joystick test scripti - Joystick bağlantısını ve butonları test eder
"""

import pygame
import sys
import time

def test_joystick():
    """Joystick'i test et"""
    print("🎮 Joystick Test Başlatılıyor...")
    
    # Pygame başlat
    try:
        pygame.init()
        pygame.joystick.init()
        print("✓ Pygame başlatıldı")
    except Exception as e:
        print(f"✗ Pygame hatası: {e}")
        return
    
    # Joystick sayısını kontrol et
    joystick_count = pygame.joystick.get_count()
    print(f"📊 Bulunan joystick sayısı: {joystick_count}")
    
    if joystick_count == 0:
        print("✗ Hiç joystick bulunamadı!")
        print("  Lütfen bir gamepad/joystick bağlayın ve tekrar deneyin.")
        return
    
    # İlk joystick'i kullan
    joystick = pygame.joystick.Joystick(0)
    joystick.init()
    
    print(f"\n🎮 Joystick Bilgileri:")
    print(f"  İsim: {joystick.get_name()}")
    print(f"  Axis sayısı: {joystick.get_numaxes()}")
    print(f"  Buton sayısı: {joystick.get_numbuttons()}")
    print(f"  Hat sayısı: {joystick.get_numhats()}")
    
    print(f"\n📋 Test Modu - Çıkmak için Ctrl+C")
    print("=" * 50)
    
    try:
        clock = pygame.time.Clock()
        
        while True:
            pygame.event.pump()
            
            # Ekranı temizle
            import os
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print("🎮 Joystick Test - Gerçek Zamanlı")
            print("=" * 50)
            
            # Axis değerleri
            print("📊 Axis Değerleri:")
            for i in range(joystick.get_numaxes()):
                value = joystick.get_axis(i)
                bar = "█" * int(abs(value) * 20)
                print(f"  Axis {i}: {value:6.3f} |{bar:<20}|")
            
            print()
            
            # Buton durumları
            print("🔘 Buton Durumları:")
            pressed_buttons = []
            for i in range(joystick.get_numbuttons()):
                if joystick.get_button(i):
                    pressed_buttons.append(str(i))
            
            if pressed_buttons:
                print(f"  Basılı butonlar: {', '.join(pressed_buttons)}")
            else:
                print("  Hiç buton basılı değil")
            
            print()
            
            # Hat durumları (D-pad)
            if joystick.get_numhats() > 0:
                print("🧭 Hat (D-pad) Durumları:")
                for i in range(joystick.get_numhats()):
                    hat_value = joystick.get_hat(i)
                    print(f"  Hat {i}: {hat_value}")
            
            print("\n" + "=" * 50)
            print("Xbox Controller Referansı:")
            print("Axis 0: Sol Stick X    Axis 1: Sol Stick Y")
            print("Axis 2: Sağ Stick Y    Axis 3: Sağ Stick X") 
            print("Axis 4: Sol Trigger    Axis 5: Sağ Trigger")
            print("Buton 0: A    Buton 1: B    Buton 2: X    Buton 3: Y")
            print("Buton 4: LB   Buton 5: RB   Buton 6: Back Buton 7: Start")
            print("\nÇıkmak için Ctrl+C")
            
            clock.tick(10)  # 10 FPS
            
    except KeyboardInterrupt:
        print("\n✓ Test sonlandırıldı")
    except Exception as e:
        print(f"\n✗ Test hatası: {e}")
    finally:
        joystick.quit()
        pygame.quit()

if __name__ == "__main__":
    test_joystick()
